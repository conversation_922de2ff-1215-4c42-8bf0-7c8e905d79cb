"use client"

import { useState } from "react"
import { ArrowUpDown, <PERSON>Up, ArrowDown } from "lucide-react"

type SortDirection = "asc" | "desc" | null

interface FilterTabsProps {
  className?: string
}

export function VelozioFilterTabs({ className = "" }: FilterTabsProps) {
  const [activeTab, setActiveTab] = useState("terkait")
  const [sortDirection, setSortDirection] = useState<SortDirection>(null)

  const handleTabClick = (tab: string) => {
    // Reset sort direction if switching away from "harga" tab
    if (activeTab === "harga" && tab !== "harga") {
      setSortDirection(null)
    }

    setActiveTab(tab)
  }

  const handleSortClick = () => {
    setActiveTab("harga")

    if (sortDirection === null) {
      setSortDirection("asc")
    } else if (sortDirection === "asc") {
      setSortDirection("desc")
    } else {
      setSortDirection("asc")
    }
  }

  return (
    <div className={`w-full bg-white border-b border-gray-200 ${className}`}>
      <div className="max-w-[800px] mx-auto overflow-x-auto">
        <div className="flex min-w-full">
          <button
            className={`flex-1 py-3 px-4 text-sm font-medium transition-all duration-200 relative ${
              activeTab === "terkait" ? "text-[#ee4d2d]" : "text-gray-600 hover:text-[#ee4d2d]"
            }`}
            onClick={() => handleTabClick("terkait")}
          >
            Terkait
            {activeTab === "terkait" && <div className="absolute bottom-0 left-0 w-full h-[2px] bg-[#ee4d2d]"></div>}
          </button>

          <button
            className={`flex-1 py-3 px-4 text-sm font-medium transition-all duration-200 relative ${
              activeTab === "terlaris" ? "text-[#ee4d2d]" : "text-gray-600 hover:text-[#ee4d2d]"
            }`}
            onClick={() => handleTabClick("terlaris")}
          >
            Terlaris
            {activeTab === "terlaris" && <div className="absolute bottom-0 left-0 w-full h-[2px] bg-[#ee4d2d]"></div>}
          </button>

          <button
            className={`flex-1 py-3 px-4 text-sm font-medium transition-all duration-200 relative ${
              activeTab === "terbaru" ? "text-[#ee4d2d]" : "text-gray-600 hover:text-[#ee4d2d]"
            }`}
            onClick={() => handleTabClick("terbaru")}
          >
            Terbaru
            {activeTab === "terbaru" && <div className="absolute bottom-0 left-0 w-full h-[2px] bg-[#ee4d2d]"></div>}
          </button>

          <button
            className={`flex-1 py-3 px-4 text-sm font-medium transition-all duration-200 relative flex items-center justify-center gap-1 ${
              activeTab === "harga" ? "text-[#ee4d2d]" : "text-gray-600 hover:text-[#ee4d2d]"
            }`}
            onClick={handleSortClick}
            onMouseEnter={() => {
              // Ensure hover effect works consistently
              document.activeElement?.blur()
            }}
          >
            Harga
            {sortDirection === null && <ArrowUpDown size={14} />}
            {sortDirection === "asc" && <ArrowUp size={14} />}
            {sortDirection === "desc" && <ArrowDown size={14} />}
            {activeTab === "harga" && <div className="absolute bottom-0 left-0 w-full h-[2px] bg-[#ee4d2d]"></div>}
          </button>
        </div>
      </div>
    </div>
  )
}
