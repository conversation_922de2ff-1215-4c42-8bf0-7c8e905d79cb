'use client'

import { useEffect } from 'react'

interface KeywordPredictionsProps {
  searchValue: string
  onPredictionClick: (keyword: string) => void
}

const KeywordPredictions = ({ searchValue, onPredictionClick }: KeywordPredictionsProps) => {
  // Keyword predictions data - sesuai docs/facet.html
  const keywordPredictions = [
    'tas',
    'tas wanita', 
    'tas pria',
    'tas sekolah',
    'tas ransel',
    'tas selempang',
    'tas laptop',
    'tas travel',
    'tas kulit',
    'tas branded',
    'tas murah',
    'tas import',
    'tas lokal',
    'tas vintage',
    'tas kantor'
  ]

  const searchHistory = ['tas', 'divf', 'tas mata', 't', 'tas sekolah', 'tas selempang']

  // Function to highlight matching text
  const highlightMatchingText = (text: string, query: string) => {
    if (!query) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)
    
    return parts.map((part, index) => {
      if (part.toLowerCase() === query.toLowerCase()) {
        return `<span class="highlighted">${part}</span>`
      }
      return part
    }).join('')
  }

  // Function to get prediction icon
  const getPredictionIcon = (keyword: string, query: string) => {
    const isMatched = keyword.toLowerCase().includes(query.toLowerCase())
    
    if (keyword.includes('tas')) {
      return isMatched ? 'fa-cart-shopping matched' : 'fa-cart-shopping'
    } else if (searchHistory.includes(keyword)) {
      return isMatched ? 'fa-history matched' : 'fa-history'
    } else {
      return isMatched ? 'fa-arrow-trend-up matched' : 'fa-arrow-trend-up'
    }
  }

  // Function to show predictions - CONTAINER TERPISAH dari suggestions
  const showPredictions = (query: string) => {
    const predictionsContainer = document.getElementById('keywordPredictions')
    if (!predictionsContainer) return

    if (query.length === 0) {
      predictionsContainer.style.display = 'none'
      return
    }

    const filteredPredictions = keywordPredictions.filter(keyword =>
      keyword.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 8) // Maksimal 8 prediksi

    if (filteredPredictions.length === 0) {
      predictionsContainer.style.display = 'none'
      return
    }

    const predictionsHTML = filteredPredictions.map(keyword => {
      const iconClass = getPredictionIcon(keyword, query)
      const highlightedText = highlightMatchingText(keyword, query)
      
      return `
        <div class="prediction-item" onclick="window.handlePredictionClick('${keyword}')">
          <span class="prediction-icon ${iconClass}">
            <i class="fa ${iconClass.split(' ')[0]}"></i>
          </span>
          <span class="prediction-text">${highlightedText}</span>
        </div>
      `
    }).join('')

    predictionsContainer.innerHTML = predictionsHTML
    predictionsContainer.style.display = 'block'
  }

  // Function to hide predictions
  const hidePredictions = () => {
    const predictionsContainer = document.getElementById('keywordPredictions')
    if (predictionsContainer) {
      predictionsContainer.style.display = 'none'
    }
  }

  // Effect untuk handle search value changes
  useEffect(() => {
    if (searchValue.length > 0) {
      showPredictions(searchValue)
    } else {
      hidePredictions()
    }
  }, [searchValue])

  // Make prediction click handler global
  useEffect(() => {
    // @ts-ignore
    window.handlePredictionClick = onPredictionClick
    return () => {
      // @ts-ignore
      delete window.handlePredictionClick
    }
  }, [onPredictionClick])

  return (
    <div className="keyword-predictions" id="keywordPredictions" style={{ display: 'none' }}>
      {/* Content will be filled dynamically by JavaScript */}
    </div>
  )
}

export default KeywordPredictions
