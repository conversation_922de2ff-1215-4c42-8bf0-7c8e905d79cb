"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // <PERSON>an auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
    if (value.length > 0) {
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    } else if (!isSearchExpanded) {
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setSearchValue('')
      document.body.classList.remove('show-suggestions')
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    }
  }



  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Bisa ditambahkan logic untuk navigate atau search
    console.log('Suggestion clicked:', suggestion)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Overlay untuk expanded mode */}
        {showSuggestions && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onToggleExpanded={handleToggleExpanded}
      />

      {/* Main Content */}
      <main className="pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>
          
          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Suggestions Container - Persis seperti docs/facet.html */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          {/* Clear history option - sesuai facet.html */}
          <div className="clear-history" onClick={() => console.log('Clear history')}>
            <i className="fas fa-trash-alt" style={{ marginRight: '8px', fontSize: '12px' }}></i>
            Hapus riwayat pencarian
          </div>

          {/* Current search suggestion - selalu dalam bentuk list */}
          <div className="current-search-suggestion">
            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick(searchValue || 'Cari produk, brand, atau toko')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-search"></i>
              </div>
              <span className="suggestion-text">
                {searchValue || 'Cari produk, brand, atau toko'}
              </span>
            </div>
          </div>

          {/* Text suggestions - SEBELUM klik "Lihat Lainnya" = bentuk list */}
          {!showMoreSuggestions && (
            <div className="text-suggestions-list" id="textSuggestions">
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('sepatu sneakers')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">sepatu sneakers</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('tas wanita')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">tas wanita</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('smartphone android')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">smartphone android</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('headphone bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">headphone bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('keyboard gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">keyboard gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('mouse wireless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">mouse wireless</span>
              </div>
            </div>
          )}

          {/* Text suggestions - SETELAH klik "Lihat Lainnya" = bentuk list dengan semua item */}
          {showMoreSuggestions && (
            <div className="text-suggestions-expanded" id="textSuggestionsExpanded">
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('sepatu sneakers')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">sepatu sneakers</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('tas wanita')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">tas wanita</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('smartphone android')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">smartphone android</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('headphone bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">headphone bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('keyboard gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">keyboard gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('mouse wireless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">mouse wireless</span>
              </div>

              {/* Extended suggestions - tambahan setelah expand */}
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('kamera mirrorless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">kamera mirrorless</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('jam tangan pintar')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">jam tangan pintar</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('speaker bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">speaker bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('laptop gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">laptop gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('power bank 20000mah')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">power bank 20000mah</span>
              </div>
            </div>
          )}

          {/* Sedang Trend section - sesuai docs/facet.html */}
          <div className="trending-section">
            <div className="trend-pill">
              Sedang Trend
              <div className="trend-pill-badge">5</div>
            </div>

            {/* Keyword suggestions - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */}
            {!showMoreSuggestions && (
              <div className="keyword-suggestions-grid">
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas')}
                >
                  tas
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('divf')}
                >
                  divf
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas mata')}
                >
                  tas mata
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('t')}
                >
                  t
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas sekolah')}
                >
                  tas sekolah
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas selempang')}
                >
                  tas selempang
                </div>
              </div>
            )}

            {/* Keyword suggestions - SETELAH klik "Lihat Lainnya" = bentuk list */}
            {showMoreSuggestions && (
              <div className="keyword-suggestions-list">
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('divf')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">divf</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas mata')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas mata</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('t')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">t</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas sekolah')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas sekolah</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas selempang')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas selempang</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas se')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas se</span>
                </div>

                {/* Additional trending items */}
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Sekolah')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Sekolah</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Selempang')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Selempang</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Handphone')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Handphone</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Mata')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Mata</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas</span>
                </div>
              </div>
            )}

            {/* Tombol Lihat Lainnya/Sembunyikan - sesuai docs/facet.html */}
            <div className="see-more-container">
              <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
                {!showMoreSuggestions ? (
                  <>
                    <i className="fas fa-plus-circle"></i>
                    <span>Lihat Lainnya</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-minus-circle"></i>
                    <span>Sembunyikan</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Product suggestions dengan keyword tags - sesuai facet.html */}
          <div className="keyword-suggestions-popup" id="keywordSuggestionsPopup">
            <div className="keyword-suggestions-title">Produk Populer</div>
            <div className="keyword-suggestions-grid">
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('iPhone 15 Pro')}
              >
                iPhone 15 Pro
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('Samsung Galaxy S24')}
              >
                Samsung Galaxy S24
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('MacBook Air M3')}
              >
                MacBook Air M3
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('AirPods Pro')}
              >
                AirPods Pro
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('iPad Pro')}
              >
                iPad Pro
              </div>
              <div
                className="keyword-suggestion-tag"
                onClick={() => handleSuggestionClick('Apple Watch')}
              >
                Apple Watch
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}

export default SellzioPage
