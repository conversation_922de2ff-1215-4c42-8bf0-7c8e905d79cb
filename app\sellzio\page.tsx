"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // Jangan auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
    if (value.length > 0) {
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    } else if (!isSearchExpanded) {
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setSearchValue('')
      document.body.classList.remove('show-suggestions')
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    }
  }



  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Bisa ditambahkan logic untuk navigate atau search
    console.log('Suggestion clicked:', suggestion)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Overlay untuk expanded mode */}
        {showSuggestions && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onToggleExpanded={handleToggleExpanded}
      />

      {/* Main Content */}
      <main className="pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>
          
          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Suggestions Container */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          <div className="text-suggestions">
            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick(searchValue || 'Cari produk, brand, atau toko')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-search"></i>
              </div>
              <span className="suggestion-text">
                {searchValue || 'Cari produk, brand, atau toko'}
              </span>
            </div>

            {/* Sample suggestions */}
            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('sepatu sneakers')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">sepatu sneakers</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('tas wanita')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">tas wanita</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('smartphone')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">smartphone</span>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}

export default SellzioPage
