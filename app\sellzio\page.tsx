"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // <PERSON>an auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
    if (value.length > 0) {
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    } else if (!isSearchExpanded) {
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setSearchValue('')
      document.body.classList.remove('show-suggestions')
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    }
  }



  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Bisa ditambahkan logic untuk navigate atau search
    console.log('Suggestion clicked:', suggestion)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Overlay untuk expanded mode */}
        {showSuggestions && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onToggleExpanded={handleToggleExpanded}
      />

      {/* Main Content */}
      <main className="pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>
          
          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Suggestions Container - Persis seperti docs/facet.html */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          {/* Clear history option - sesuai facet.html */}
          <div className="clear-history" onClick={() => console.log('Clear history')}>
            <i className="fas fa-trash-alt" style={{ marginRight: '8px', fontSize: '12px' }}></i>
            Hapus riwayat pencarian
          </div>

          {/* Keyword suggestions di bagian atas - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag sesuai docs/facet.html */}
          {!showMoreSuggestions && searchHistory.length > 0 && (
            <div className="keyword-button-container">
              {getInitialKeywords().map((keyword, index) => (
                <div
                  key={index}
                  className="keyword-button"
                  onClick={() => handleSuggestionClick(keyword)}
                >
                  <span className="suggestion-icon">
                    <i className="fa fa-history"></i>
                  </span>
                  <span className="keyword-button-text">{keyword}</span>
                </div>
              ))}
            </div>
          )}

          {/* Show empty message if no search history */}
          {!showMoreSuggestions && searchHistory.length === 0 && (
            <div className="empty-history-message">
              <p>Belum ada riwayat pencarian</p>
            </div>
          )}

          {/* Keyword suggestions di bagian atas - SETELAH klik "Lihat Lainnya" = bentuk list */}
          {showMoreSuggestions && (
            <div className="main-keyword-suggestions-list">
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('sepatu sneakers')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">sepatu sneakers</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('tas wanita')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">tas wanita</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('smartphone android')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">smartphone android</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('headphone bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">headphone bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('keyboard gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">keyboard gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('mouse wireless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">mouse wireless</span>
              </div>

              {/* Extended suggestions - tambahan setelah expand */}
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('kamera mirrorless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">kamera mirrorless</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('jam tangan pintar')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">jam tangan pintar</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('speaker bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">speaker bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('laptop gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">laptop gaming</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('power bank 20000mah')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-history"></i>
                </div>
                <span className="suggestion-text">power bank 20000mah</span>
              </div>
            </div>
          )}

          {/* Tombol Lihat Lainnya untuk keyword suggestions di bagian atas */}
          <div className="main-see-more-container">
            <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
              {!showMoreSuggestions ? (
                <>
                  <i className="fas fa-plus-circle"></i>
                  <span>Lihat Lainnya</span>
                </>
              ) : (
                <>
                  <i className="fas fa-minus-circle"></i>
                  <span>Sembunyikan</span>
                </>
              )}
            </button>
          </div>

          {/* Sedang Trend section - sesuai docs/facet.html */}
          <div className="trending-section">
            <div className="trend-pill">
              Sedang Trend
              <div className="trend-pill-badge">5</div>
            </div>

            {/* Keyword suggestions - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */}
            {!showMoreSuggestions && (
              <div className="keyword-suggestions-grid">
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas')}
                >
                  tas
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('divf')}
                >
                  divf
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas mata')}
                >
                  tas mata
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('t')}
                >
                  t
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas sekolah')}
                >
                  tas sekolah
                </div>
                <div
                  className="keyword-suggestion-tag"
                  onClick={() => handleSuggestionClick('tas selempang')}
                >
                  tas selempang
                </div>
              </div>
            )}

            {/* Keyword suggestions - SETELAH klik "Lihat Lainnya" = bentuk list */}
            {showMoreSuggestions && (
              <div className="keyword-suggestions-list">
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('divf')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">divf</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas mata')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas mata</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('t')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">t</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas sekolah')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas sekolah</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas selempang')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas selempang</span>
                </div>
                <div
                  className="suggestion-item"
                  onClick={() => handleSuggestionClick('tas se')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-history"></i>
                  </div>
                  <span className="suggestion-text">tas se</span>
                </div>

                {/* Additional trending items */}
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Sekolah')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Sekolah</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Selempang')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Selempang</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Handphone')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Handphone</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas Mata')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas Mata</span>
                </div>
                <div
                  className="suggestion-item trending-item"
                  onClick={() => handleSuggestionClick('Tas')}
                >
                  <div className="suggestion-icon">
                    <i className="fas fa-arrow-trend-up"></i>
                  </div>
                  <span className="suggestion-text">Tas</span>
                </div>
              </div>
            )}

            {/* Tombol Lihat Lainnya/Sembunyikan - sesuai docs/facet.html */}
            <div className="see-more-container">
              <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
                {!showMoreSuggestions ? (
                  <>
                    <i className="fas fa-plus-circle"></i>
                    <span>Lihat Lainnya</span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-minus-circle"></i>
                    <span>Sembunyikan</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Produk Populer section - bentuk card sesuai docs/velozio-facet-implementation.md */}
          <div className="popular-products-section">
            <div className="popular-products-title">Produk Populer</div>
            <div className="popular-products-card-grid">
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Samsung Galaxy')}
              >
                <img src="/api/placeholder/120/120" alt="Samsung Galaxy" className="product-card-image" />
                <div className="product-card-title">Samsung Galaxy</div>
              </div>
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Sneakers Pria')}
              >
                <img src="/api/placeholder/120/120" alt="Sneakers Pria" className="product-card-image" />
                <div className="product-card-title">Sneakers Pria</div>
              </div>
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <img src="/api/placeholder/120/120" alt="Tas Selempang" className="product-card-image" />
                <div className="product-card-title">Tas Selempang</div>
              </div>
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Headphone Bluetooth')}
              >
                <img src="/api/placeholder/120/120" alt="Headphone Bluetooth" className="product-card-image" />
                <div className="product-card-title">Headphone Bluetooth</div>
              </div>
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Keyboard Gaming')}
              >
                <img src="/api/placeholder/120/120" alt="Keyboard Gaming" className="product-card-image" />
                <div className="product-card-title">Keyboard Gaming</div>
              </div>
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Power Bank')}
              >
                <img src="/api/placeholder/120/120" alt="Power Bank" className="product-card-image" />
                <div className="product-card-title">Power Bank</div>
              </div>
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Smart TV')}
              >
                <img src="/api/placeholder/120/120" alt="Smart TV" className="product-card-image" />
                <div className="product-card-title">Smart TV</div>
              </div>
              <div
                className="popular-product-card"
                onClick={() => handleSuggestionClick('Tas Selempang')}
              >
                <img src="/api/placeholder/120/120" alt="Tas Selempang" className="product-card-image" />
                <div className="product-card-title">Tas Selempang</div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}

export default SellzioPage
