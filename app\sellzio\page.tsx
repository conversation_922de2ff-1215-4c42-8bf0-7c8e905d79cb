"use client"

import React, { useState, useEffect } from 'react'
import { SellzioHeader } from '@/components/themes/sellzio/sellzio-header'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'

const SellzioPage = () => {
  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)

  // Handle search focus
  const handleSearchFocus = () => {
    setIsSearchExpanded(true)
    setShowSuggestions(true)
    // Add class to body to hide main content
    document.body.classList.add('show-suggestions')
  }

  // Handle search blur - hanya untuk non-expanded mode
  const handleSearchBlur = () => {
    // <PERSON>an auto-close di expanded mode
    if (!isSearchExpanded) {
      setTimeout(() => {
        if (!searchValue) {
          setShowSuggestions(false)
          document.body.classList.remove('show-suggestions')
        }
      }, 150)
    }
  }

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
    if (value.length > 0) {
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    } else if (!isSearchExpanded) {
      setShowSuggestions(false)
      document.body.classList.remove('show-suggestions')
    }
  }

  // Handle toggle expanded
  const handleToggleExpanded = () => {
    if (isSearchExpanded) {
      // Collapsing
      setIsSearchExpanded(false)
      setShowSuggestions(false)
      setSearchValue('')
      document.body.classList.remove('show-suggestions')
    } else {
      // Expanding
      setIsSearchExpanded(true)
      setShowSuggestions(true)
      document.body.classList.add('show-suggestions')
    }
  }



  // Handle suggestion click
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Bisa ditambahkan logic untuk navigate atau search
    console.log('Suggestion clicked:', suggestion)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('show-suggestions')
    }
  }, [])

  return (
    <>
      <Head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Overlay untuk expanded mode */}
        {showSuggestions && (
          <div className="overlay show"></div>
        )}
      
      {/* Header */}
      <SellzioHeader
        searchValue={searchValue}
        isExpanded={isSearchExpanded}
        onSearchFocus={handleSearchFocus}
        onSearchBlur={handleSearchBlur}
        onSearchChange={handleSearchChange}
        onToggleExpanded={handleToggleExpanded}
      />

      {/* Main Content */}
      <main className="pt-20 container mx-auto py-4 px-2">
        <section className="mb-12">
          <h1 className="text-2xl font-bold text-center mb-8">
            Selamat Datang di Sellzio
          </h1>
          
          {/* Placeholder content */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
                <h3 className="text-lg font-semibold mb-2">Produk {index + 1}</h3>
                <p className="text-gray-600 mb-4">
                  Deskripsi produk yang menarik dan informatif untuk produk {index + 1}.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-orange-500">
                    Rp {(index + 1) * 100}.000
                  </span>
                  <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    Beli Sekarang
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* Suggestions Container - Sesuai docs/facet.html */}
      {showSuggestions && (
        <div className="suggestions-container" onClick={(e) => e.stopPropagation()}>
          <div className="text-suggestions">
            {/* Current search suggestion */}
            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick(searchValue || 'Cari produk, brand, atau toko')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-search"></i>
              </div>
              <span className="suggestion-text">
                {searchValue || 'Cari produk, brand, atau toko'}
              </span>
            </div>

            {/* Clear history option */}
            <div className="clear-history" onClick={() => console.log('Clear history')}>
              <i className="fas fa-trash-alt" style={{ marginRight: '8px', fontSize: '12px' }}></i>
              Hapus riwayat pencarian
            </div>

            {/* Search history suggestions */}
            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('sepatu sneakers')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">sepatu sneakers</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('tas wanita')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">tas wanita</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('smartphone android')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">smartphone android</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('headphone bluetooth')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">headphone bluetooth</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('keyboard gaming')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-clock"></i>
              </div>
              <span className="suggestion-text">keyboard gaming</span>
            </div>
          </div>

          {/* Trending section */}
          <div className="trending-title">
            Sedang Trend
            <i className="fas fa-arrow-trend-up trend-icon"></i>
          </div>

          {/* Trending suggestions */}
          <div className="additional-suggestions">
            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('robot vacuum')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-arrow-trend-up"></i>
              </div>
              <span className="suggestion-text">robot vacuum</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('smart tv 55 inch')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-arrow-trend-up"></i>
              </div>
              <span className="suggestion-text">smart tv 55 inch</span>
            </div>

            <div
              className="suggestion-item"
              onClick={() => handleSuggestionClick('power bank 20000mah')}
            >
              <div className="suggestion-icon">
                <i className="fas fa-arrow-trend-up"></i>
              </div>
              <span className="suggestion-text">power bank 20000mah</span>
            </div>
          </div>

          {/* See more button */}
          <div className="see-more-container">
            <button className="see-more-btn" onClick={() => setShowMoreSuggestions(!showMoreSuggestions)}>
              <i className="fas fa-chevron-down"></i>
              Lihat Lainnya
            </button>
          </div>

          {/* Additional suggestions (expandable) */}
          {showMoreSuggestions && (
            <div className="additional-suggestions open">
              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('kamera mirrorless')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-camera"></i>
                </div>
                <span className="suggestion-text">kamera mirrorless</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('jam tangan pintar')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-clock"></i>
                </div>
                <span className="suggestion-text">jam tangan pintar</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('speaker bluetooth')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-volume-up"></i>
                </div>
                <span className="suggestion-text">speaker bluetooth</span>
              </div>

              <div
                className="suggestion-item"
                onClick={() => handleSuggestionClick('laptop gaming')}
              >
                <div className="suggestion-icon">
                  <i className="fas fa-laptop"></i>
                </div>
                <span className="suggestion-text">laptop gaming</span>
              </div>
            </div>
          )}

          {/* Product suggestions */}
          <div className="product-suggestions">
            <div className="product-title">Produk untuk Anda</div>
            <div className="product-grid">
              <div className="simple-product-card" onClick={() => handleSuggestionClick('iPhone 15 Pro')}>
                <img src="https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=150&h=150&fit=crop" alt="iPhone" className="product-img" />
                <div className="simple-product-name">iPhone 15 Pro</div>
              </div>
              <div className="simple-product-card" onClick={() => handleSuggestionClick('Samsung Galaxy S24')}>
                <img src="https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=150&h=150&fit=crop" alt="Samsung" className="product-img" />
                <div className="simple-product-name">Samsung Galaxy S24</div>
              </div>
              <div className="simple-product-card" onClick={() => handleSuggestionClick('MacBook Air M3')}>
                <img src="https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=150&h=150&fit=crop" alt="MacBook" className="product-img" />
                <div className="simple-product-name">MacBook Air M3</div>
              </div>
              <div className="simple-product-card" onClick={() => handleSuggestionClick('AirPods Pro')}>
                <img src="https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=150&h=150&fit=crop" alt="AirPods" className="product-img" />
                <div className="simple-product-name">AirPods Pro</div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}

export default SellzioPage
