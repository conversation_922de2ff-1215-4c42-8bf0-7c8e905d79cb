@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

/* Animation for placeholder text - matches reference more closely */
@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
    visibility: hidden;
  }
  1%,
  5% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
  6%,
  100% {
    opacity: 0;
    transform: translateY(-20px);
    visibility: hidden;
  }
}

/* Animasi panah bergerak */
@keyframes arrowMove {
  0% { transform: translateX(0); }
  50% { transform: translateX(3px); }
  100% { transform: translateX(0); }
}

/* CSS untuk tombol floating arrow */
.floating-arrow {
  position: absolute;
  right: 5px;
  top: 45%;
  transform: translateY(-50%);
  height: 36px;
  background-color: rgba(255, 245, 240, 0.95);
  border: 1px solid #FFDFD1;
  border-radius: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  padding: 0 15px 0 12px;
  cursor: pointer;
  z-index: 10;
  transition: opacity 0.8s ease, transform 0.8s ease;
  opacity: 1;
}

.floating-arrow.hidden {
  opacity: 0;
  transform: translateY(-50%) translateX(10px);
  pointer-events: none;
}

.floating-arrow:active {
  transform: translateY(-50%) scale(0.97);
}

.floating-arrow-text {
  font-size: 12px;
  font-weight: 600;
  color: #FF5722;
  margin-right: 6px;
}

.floating-arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-arrow-icon svg {
  width: 15px;
  height: 15px;
  color: #FF5722;
  animation: arrowMove 1.5s infinite ease-in-out;
}

/* Kategori Styles */
.categories-container {
  display: flex;
  overflow-x: auto;
  padding-bottom: 10px;
  gap: 12px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  padding: 8px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  cursor: pointer;
}

.category-item:hover {
  transform: translateY(-2px);
}

.category-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-name {
  font-size: 12px;
  text-align: center;
  color: #333;
  line-height: 1.2;
}

/* Desktop Categories Grid */
.desktop-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
  grid-template-rows: repeat(2, auto);
  grid-auto-flow: row;
  gap: 12px;
  padding: 4px 16px 12px 4px;
  width: 100%;
}

/* Gradient untuk efek fade di sisi kanan */
.categories-container-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
  pointer-events: none;
}

/* CSS untuk expanded categories */
.expanded-categories {
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease;
}

.expanded-categories.active {
  height: auto !important;
  overflow: visible;
}

.expanded-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  padding: 5px 10px 10px 10px;
}

@media (min-width: 768px) {
  .expanded-grid {
    grid-template-columns: repeat(7, 1fr);
    gap: 15px;
  }
}

.placeholder-animate {
  animation: placeholderAnimation 45s ease-in-out infinite;
}

/* Search input styling */
.search-input::placeholder {
  color: #666;
  opacity: 1;
}

.search-input:focus::placeholder {
  opacity: 0.7;
}

/* Cart and chat icon styling */
.cart-icon,
.chat-icon {
  position: relative;
  color: transparent;
  font-size: 22px;
  cursor: pointer;
}

/* For webkit browsers to create outline effect on icons */
@supports (-webkit-text-stroke: 1px white) {
  .cart-icon {
    -webkit-text-stroke: 1.3px white;
  }

  .chat-icon {
    -webkit-text-stroke: 2px white;
  }
}

/* For non-webkit browsers fallback */
@supports not (-webkit-text-stroke: 1px white) {
  .cart-icon,
  .chat-icon {
    color: white;
    text-shadow: 0 0 1px white;
  }
}

/* Suggestions Container Styles */
.suggestions-container {
  background-color: white;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  margin-top: 10px;
  border-radius: 3px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 0;
  position: relative;
  transform: translateY(10px);
  z-index: 1001;
}

/* Overlay putih solid untuk menutupi halaman utama - persis sesuai docs/facet.html */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 999;
  display: none;
  overflow-y: auto;
  box-sizing: border-box;
}

.overlay.show {
  display: block !important;
}

/* Prevent body scroll when suggestions are shown */
body.show-suggestions {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Text suggestions */
.text-suggestions {
  padding: 10px 0px 10px 0px;
  border-bottom: 1px solid #f2f2f2;
  display: block;
}

/* Styling yang konsisten untuk suggestion-icon */
.suggestion-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  font-size: 14px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.suggestion-icon i {
  font-size: 14px;
  color: #999;
}

/* Icon styling untuk trending */
.additional-suggestions .suggestion-icon i {
  color: #ee4d2d !important;
  font-size: 14px;
}

/* Styling untuk batas antar item sugesti */
.suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

/* Container teks untuk item sugesti */
.suggestion-text {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  color: #333;
  font-size: 14px;
}

.suggestion-item:last-child {
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Styling khusus untuk ikon trend */
.additional-suggestions .suggestion-icon {
  font-size: 18px !important;
  margin-bottom: 6px;
  color: #ee4d2d;
}

/* Style untuk tombol Lihat Lainnya */
.see-more-container {
  padding: 5px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
}

.see-more-btn {
  background-color: transparent;
  color: #ee4d2d;
  border: none;
  padding: 10px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 500;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.see-more-btn:hover {
  background-color: transparent;
}

.see-more-btn svg {
  margin-right: 8px;
  color: #ee4d2d;
  font-size: 14px;
}

.see-more-icon {
  margin-right: 8px;
  display: inline-flex;
  align-items: center;
}

.see-more-icon i {
  color: #ee4d2d;
  font-size: 16px;
}

/* Style untuk additional suggestions dengan animasi slide down */
.additional-suggestions {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  border-bottom: 0px solid #f2f2f2;
  background-color: white;
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
  display: block;
}

.additional-suggestions.open {
  max-height: 500px;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 20px;
}

.additional-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.additional-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Styling untuk "Hapus riwayat pencarian" */
.clear-history {
  text-align: center;
  padding: 10px;
  margin-bottom: 5px;
  cursor: pointer;
  color: #999;
  font-size: 13px;
  border-bottom: 1px solid #f2f2f2;
  transition: color 0.2s ease;
}

.clear-history:hover {
  color: #ee4d2d;
}

/* Styling untuk item sugesti bentuk tombol */
.keyword-button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 10px 15px;
}

.keyword-button {
  display: inline-flex;
  align-items: flex-start;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: normal;
  max-width: 100%;
}

.keyword-button:hover {
  border-color: #ee4d2d;
  color: #ee4d2d;
  background-color: #fff9f8;
}

.keyword-button .suggestion-icon {
  margin-right: 8px;
  color: #999;
  flex-shrink: 0;
  margin-top: 2px;
}

.keyword-button-text {
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  word-break: break-word;
}

/* Styling untuk "Sedang Trend" */
.trending-title {
  margin-top: 15px;
  color: #333;
  font-weight: 500;
  padding: 12px 15px 5px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.trending-title .trend-icon {
  margin-left: 5px;
  font-size: 13px;
  color: #ee4d2d;
}

/* Product suggestions */
.product-suggestions {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}

.product-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 5px;
  display: block;
}

/* Grid produk standar */
.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
  box-sizing: border-box;
  border: 2px solid #eee;
  border-radius: 5px;
  margin-top: 30px;
  padding: 10px 10px 25px 10px;
  transform: translateY(0px);
}

/* Card produk simpel (tampilan pencarian awal) */
.simple-product-card {
  background-color: white;
  border-radius: 3px;
  transform: translateY(10px);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-align: center;
}

.simple-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.product-img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

/* Nama produk untuk tampilan simpel */
.simple-product-name {
  font-size: 13px;
  color: #333;
  padding: 10px 5px;
  text-align: center;
  height: auto;
  max-height: 40px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.product-price {
  font-size: 14px;
  color: #ee4d2d;
  font-weight: bold;
  padding: 0 5px 10px 5px;
}

/* Keyword Predictions Container Styles */
.keyword-predictions-container {
  background-color: white;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  margin-top: 10px;
  border-radius: 3px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 0;
  position: relative;
  transform: translateY(10px);
  z-index: 1001;
}

.prediction-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.prediction-item:hover {
  background-color: #f9f9f9;
}

.prediction-item:last-child {
  border-bottom: none;
}

.prediction-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  font-size: 14px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.prediction-icon i {
  font-size: 14px;
  color: #999;
}

.prediction-text {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  color: #333;
  font-size: 14px;
}

/* Highlight keyword - sesuai docs/facet.html */
.prediction-text .highlighted {
  color: #ee4d2d;
  font-weight: bold;
}

/* Untuk ikon yang cocok, gunakan warna oranye - sesuai docs/facet.html */
.prediction-icon.matched {
  color: #ee4d2d !important;
}

.prediction-icon.matched i {
  color: #ee4d2d !important;
}

/* Not Found Container Styles */
.not-found-container {
  text-align: center;
  padding: 30px 20px 40px;
  margin: 0;
  background-color: #fff;
  width: 100%;
  position: relative;
}

.not-found-icon {
  margin: 0 auto 20px;
  width: 120px;
  height: 120px;
  opacity: 0.5;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.search-document-icon {
  position: relative;
  width: 80px;
  height: 100px;
}

.document-base {
  width: 60px;
  height: 80px;
  background-color: #ddd;
  border-radius: 4px;
  position: absolute;
  top: 10px;
  left: 10px;
}

.document-fold {
  width: 15px;
  height: 15px;
  background-color: #bbb;
  position: absolute;
  top: 10px;
  right: 5px;
  clip-path: polygon(0 0, 100% 100%, 0 100%);
}

.document-lines {
  position: absolute;
  top: 25px;
  left: 20px;
  width: 40px;
}

.document-lines::before,
.document-lines::after {
  content: '';
  display: block;
  height: 2px;
  background-color: #bbb;
  margin-bottom: 5px;
}

.magnifying-glass {
  width: 30px;
  height: 30px;
  border: 3px solid #999;
  border-radius: 50%;
  position: absolute;
  bottom: -15px;
  right: -5px;
}

.magnifying-glass::after {
  content: '';
  width: 3px;
  height: 15px;
  background-color: #999;
  position: absolute;
  bottom: -15px;
  right: -5px;
  border-radius: 10px;
  transform: rotate(45deg);
}

.not-found-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.not-found-message {
  font-size: 14px;
  color: #888;
  margin-bottom: 25px;
}

.not-found-button-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  margin: 0 auto;
}

.not-found-button {
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.not-found-button.primary {
  background-color: #ee4d2d;
  color: white;
}

.not-found-button.primary:hover {
  background-color: #e04224;
}

.not-found-button.secondary {
  background-color: white;
  color: #ee4d2d;
  border: 1px solid #ee4d2d;
}

.not-found-button.secondary:hover {
  background-color: rgba(238, 77, 45, 0.05);
}

/* Responsive styles */
@media (min-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .suggestions-container {
    max-width: 800px;
    position: relative;
  }

  .keyword-predictions-container {
    max-width: 800px;
    position: relative;
  }

  .not-found-container {
    max-width: 800px;
    position: relative;
  }

  .product-img {
    height: 180px;
  }

  .simple-product-name {
    font-size: 14px;
  }
}

@media (min-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }

  .product-img {
    height: 200px;
  }
}

@media (max-width: 576px) {
  .not-found-container {
    padding: 20px 15px 30px;
    margin: 10px 0;
  }

  .not-found-button-container {
    max-width: 100%;
  }

  .not-found-message {
    max-width: 95%;
  }

  .not-found-icon {
    width: 100px;
    height: 100px;
  }

  .not-found-button {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* ===== FACET PANEL STYLING - sesuai docs/facet.html ===== */

/* Facet Section Styling */
.facet-section {
  margin-bottom: 15px;
}

.facet-section h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.facet-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.facet-section li {
  padding: 6px 0;
  display: flex;
  align-items: center;
}

.facet-section input[type="checkbox"] {
  margin-right: 8px;
  accent-color: #ee4d2d;
}

.facet-section label {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  flex: 1;
}

.facet-section label:hover {
  color: #ee4d2d;
}

.facet-section input[type="checkbox"]:checked + label {
  color: #ee4d2d;
  font-weight: 500;
}

/* Orange checkbox styling - sesuai docs/facet.html */
.orange-checkbox {
  position: relative;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  border-radius: 3px;
  outline: none;
  transition: all 0.2s ease;
  vertical-align: middle;
  margin-right: 8px;
}

.orange-checkbox:checked {
  background-color: #ee4d2d;
  border-color: #ee4d2d;
}

.orange-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 3px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Styling untuk label saat checkbox di-check */
.orange-checkbox:checked + label {
  color: #ee4d2d;
  font-weight: 500;
}

/* Active Filters Styling */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.filter-tag {
  background-color: #f5f5f5;
  border-radius: 16px;
  padding: 5px 10px;
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.filter-tag i {
  margin-left: 5px;
  color: #999;
  cursor: pointer;
}

.filter-tag i:hover {
  color: #ee4d2d;
}

/* Facet overlay untuk mobile */
.facet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: none;
  justify-content: center;
  align-items: flex-end;
}

/* Panel facet untuk mobile/tablet */
.facet-panel {
  background-color: white;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  border-radius: 10px 10px 0 0;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* Panel facet untuk desktop */
.facet-panel-desktop {
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 350px;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  z-index: 1050;
  display: none;
  flex-direction: column;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.facet-header {
  position: sticky;
  top: 0;
  display: flex;
  background-color: white;
  z-index: 10;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f2f2f2;
}

.facet-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.facet-close {
  cursor: pointer;
  color: #999;
  font-size: 18px;
  padding: 5px;
}

.facet-close:hover {
  color: #ee4d2d;
}

.facet-content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.facet-buttons {
  display: flex;
  gap: 10px;
  padding: 15px;
  border-top: 1px solid #f2f2f2;
  background-color: white;
}

.facet-button {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.facet-button-reset {
  background-color: #f5f5f5;
  color: #666;
}

.facet-button-reset:hover {
  background-color: #e8e8e8;
}

.facet-button-apply {
  background-color: #ee4d2d;
  color: white;
}

.facet-button-apply:hover {
  background-color: #d63916;
}

/* Tambahkan ruang kosong setelah konten facet terakhir */
.facet-section:last-child {
  margin-bottom: 30px;
}

/* Mencegah scroll pada body saat facet panel aktif */
body.facet-active {
  overflow: hidden;
}

/* Pastikan panel facet memiliki padding yang tepat */
.facet-panel, .facet-panel-desktop {
  overscroll-behavior: contain;
}

/* Media queries untuk responsive */
@media (min-width: 1025px) {
  .facet-panel-desktop {
    display: block;
  }
  .facet-overlay {
    display: none !important;
  }
}

/* Tambahkan media query untuk tablet */
@media (min-width: 768px) and (max-width: 1024px) {
  .facet-panel {
    max-width: 100%;
    width: 800px;
  }
}

/* ===== KEYWORD SUGGESTIONS STYLING - sesuai docs/facet.html ===== */

/* Styling untuk item sugesti bentuk tombol */
.keyword-button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 10px 15px;
}

.keyword-button {
  display: inline-flex;
  align-items: flex-start;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: normal;
  max-width: 100%;
}

.keyword-button:hover {
  border-color: #ee4d2d;
  color: #ee4d2d;
  background-color: #fff9f8;
}

.keyword-button .suggestion-icon {
  margin-right: 8px;
  color: #999;
  flex-shrink: 0;
  margin-top: 2px;
}

.keyword-button-text {
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  word-break: break-word;
}

/* Styling untuk suggestion-item (list mode) */
.suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.suggestion-item:last-child {
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-item:hover {
  background-color: #f9f9f9;
}

.suggestion-item .suggestion-icon {
  margin-right: 12px;
  color: #999;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.suggestion-item span {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  font-size: 14px;
}

/* Extended suggestions container */
.extended-suggestions {
  background-color: white;
  border-radius: 8px;
  margin: 0;
  padding: 0;
  display: none;
  transition: all 0.3s ease;
}

.extended-suggestions .suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}

.extended-suggestions .suggestion-item:last-child {
  border-bottom: 2px solid #f5f5f5;
}

.extended-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

.extended-suggestions.open {
  display: block;
}

/* ===== TREND PILL STYLING - sesuai docs/facet.html ===== */

.trend-pill {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff5f5;
  border: 1px solid #fecaca;
  border-radius: 20px;
  padding: 8px 16px;
  margin: 10px 15px;
  font-size: 14px;
  font-weight: 500;
  color: #dc2626;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trend-pill:hover {
  background-color: #fef2f2;
  border-color: #f87171;
}

.trend-pill-badge {
  background-color: #dc2626;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}
