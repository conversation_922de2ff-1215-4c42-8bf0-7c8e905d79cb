@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

/* Reset dan base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

html, body {
  width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

body {
  background-color: #f2f2f2;
  color: #333;
}

/* Header dan <PERSON> */
.header {
  background-color: #ee4d2d;
  padding: 10px 15px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.search-container {
  display: flex;
  align-items: center;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.search-input-wrapper {
  position: relative;
  flex-grow: 1;
  margin-right: 80px;
}

.search-input {
  width: 100%;
  padding: 10px 60px 10px 15px;
  border: 2px solid #ee4d2d;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  color: #333;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: relative;
  z-index: 10;
}

/* Placeholder warna oranye */
.search-input::placeholder {
  color: #ee4d2d;
  opacity: 1;
}

/* Animated placeholder */
.search-placeholder {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #ee4d2d;
  pointer-events: none;
  font-size: 14px;
  transition: opacity 0.3s;
  display: flex;
  flex-direction: column;
  width: calc(100% - 60px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 20px;
  z-index: 11;
}

.placeholder-static {
  white-space: nowrap;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 1;
  animation: staticPlaceholderAnimation 12s infinite;
}

.placeholder-dynamic {
  position: relative;
  width: 100%;
  overflow: hidden;
  height: 20px;
  max-width: calc(100% - 60px);
  line-height: 1.2;
}

.placeholder-text {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  top: 0;
  left: 0;
  animation: placeholderAnimation 45s infinite;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes staticPlaceholderAnimation {
  0%, 8% {
    opacity: 1;
  }
  12%, 96% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
    visibility: hidden;
  }
  1%, 5% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
  6%, 100% {
    opacity: 0;
    transform: translateY(-20px);
    visibility: hidden;
  }
}

.placeholder-text:nth-child(2) { animation-delay: 3s; }
.placeholder-text:nth-child(3) { animation-delay: 6s; }
.placeholder-text:nth-child(4) { animation-delay: 9s; }
.placeholder-text:nth-child(5) { animation-delay: 12s; }
.placeholder-text:nth-child(6) { animation-delay: 15s; }
.placeholder-text:nth-child(7) { animation-delay: 18s; }
.placeholder-text:nth-child(8) { animation-delay: 21s; }
.placeholder-text:nth-child(9) { animation-delay: 24s; }
.placeholder-text:nth-child(10) { animation-delay: 27s; }
.placeholder-text:nth-child(11) { animation-delay: 30s; }
.placeholder-text:nth-child(12) { animation-delay: 33s; }
.placeholder-text:nth-child(13) { animation-delay: 36s; }
.placeholder-text:nth-child(14) { animation-delay: 39s; }
.placeholder-text:nth-child(15) { animation-delay: 42s; }

/* Sembunyikan placeholder ketika input focus atau memiliki nilai */
.search-input:focus + .search-placeholder,
.search-input:not(:placeholder-shown) + .search-placeholder {
  opacity: 0 !important;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #ee4d2d;
  cursor: pointer;
  font-size: 16px;
  z-index: 15;
  background: none;
  border: none;
  padding: 5px;
}

/* Clear search icon */
.clear-search-icon {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-color: #ccc;
  border-radius: 50%;
  cursor: pointer;
  display: none;
  z-index: 30;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.clear-search-icon::before {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.clear-search-icon svg {
  color: white;
  pointer-events: none;
}

.clear-search-icon:hover {
  background-color: #aaa;
}

.clear-search-icon:active {
  background-color: #999;
  transform: translateY(-50%) scale(0.95);
}

/* Show clear icon when input has value */
.search-input:not(:placeholder-shown) ~ .clear-search-icon {
  display: flex;
}

/* Header Icons */
.header-icons {
  display: flex;
  align-items: center;
  position: absolute;
  right: 8px;
  top: 50%;
  padding-top: 5px;
  transform: translateY(-50%);
  z-index: 1;
}

.cart-icon, .chat-icon {
  position: relative;
  color: transparent;
  font-size: 22px;
  cursor: pointer;
}

.cart-icon {
  -webkit-text-stroke: 1.3px white;
}

.chat-icon {
  -webkit-text-stroke: 2px white;
  margin-left: 20px;
}

/* Badge notifikasi */
.cart-badge, .chat-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: white;
  color: #ee4d2d;
  font-size: 11px;
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

/* Chat dots */
.chat-icon .chat-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 30%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-icon .chat-dots .dot {
  width: 3px;
  height: 3px;
  background-color: white;
  border-radius: 50%;
}

/* Search expanded mode */
.search-expanded {
  background-color: white;
  padding: 10px 15px 15px 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
  box-sizing: border-box;
}

.back-btn {
  margin-right: 10px;
  color: #ee4d2d;
  cursor: pointer;
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 5px;
}

.back-btn svg {
  transform: scaleX(1.3);
}

.expanded-search-icon {
  background-color: #ee4d2d;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
  border: none;
}

/* Overlay area */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 999;
  display: none;
  overflow-y: auto;
  box-sizing: border-box;
  pointer-events: none; /* Tidak menangkap klik, hanya sebagai background */
}

.overlay.show {
  display: block !important;
}

/* Prevent body scroll when suggestions are shown */
body.show-suggestions {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Suggestions Container - Persis seperti docs/facet.html */
.suggestions-container {
  background-color: white;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  margin-top: 70px; /* Posisi sesuai facet.html */
  border-radius: 3px;
  overflow: visible; /* Tidak ada scroll di container */
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 0;
  position: fixed; /* Fixed positioning seperti facet.html */
  top: 0px;
  left: 0;
  right: 0;
  transform: translateY(10px);
  z-index: 1001;
  pointer-events: auto; /* Pastikan container dapat menerima klik */
  height: auto; /* Auto height, scroll di halaman */
  min-height: calc(100vh - 80px); /* Minimal tinggi untuk overlay effect */
}

/* Keyword button container - sesuai docs/facet.html */
.keyword-button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 10px 15px;
  border-bottom: 1px solid #f2f2f2;
}

/* Keyword button styling - persis seperti docs/facet.html */
.keyword-button {
  display: inline-flex;
  align-items: flex-start;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: normal;
  max-width: 100%;
}

.keyword-button:hover {
  border-color: #ee4d2d;
  color: #ee4d2d;
  background-color: #fff9f8;
}

.keyword-button .suggestion-icon {
  margin-right: 8px;
  color: #999;
  flex-shrink: 0;
  margin-top: 2px;
}

.keyword-button:hover .suggestion-icon {
  color: #ee4d2d;
}

.keyword-button-text {
  display: inline-block;
  text-align: left;
  line-height: 1.4;
  word-break: break-word;
}

/* Empty history message */
.empty-history-message {
  padding: 20px 15px;
  text-align: center;
  color: #999;
  font-size: 14px;
  border-bottom: 1px solid #f2f2f2;
}

/* Main keyword suggestions - SETELAH klik "Lihat Lainnya" = bentuk list */
.main-keyword-suggestions-list {
  padding: 0;
  border-bottom: 1px solid #f2f2f2;
}

.main-keyword-suggestions-list .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.main-keyword-suggestions-list .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Main see more container styling */
.main-see-more-container {
  padding: 10px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
  border-bottom: 1px solid #f2f2f2;
}

.suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f9f9f9;
}

.suggestion-item:last-child {
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  font-size: 14px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.suggestion-icon i {
  font-size: 14px;
  color: #999;
}

.suggestion-text {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  color: #333;
  font-size: 14px;
}

/* Hide main content when suggestions are shown */
body.show-suggestions main,
body.show-suggestions .container,
body.show-suggestions section {
  display: none !important;
  visibility: hidden !important;
}

/* Ensure suggestions containers are always visible */
body.show-suggestions .suggestions-container {
  display: block !important;
  visibility: visible !important;
}

/* Clear history styling - sesuai docs/facet.html */
.clear-history {
  text-align: center;
  padding: 10px;
  margin-bottom: 5px;
  cursor: pointer;
  color: #999;
  font-size: 13px;
  border-bottom: 1px solid #f2f2f2;
  transition: color 0.2s ease;
}

.clear-history:hover {
  color: #ee4d2d;
}

/* Extended suggestions styling - sesuai docs/facet.html */
.extended-suggestions {
  padding: 0;
  border-bottom: 1px solid #f2f2f2;
}

.extended-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.extended-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Trend pill styling - sesuai docs/facet.html */
.trend-pill {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 15px;
  margin: 15px 15px 10px 15px;
  background-color: #f5f5f5;
  border-radius: 20px;
  font-size: 13px;
  color: #333;
  font-weight: 500;
  position: relative;
}

.trend-pill-badge {
  background-color: #ee4d2d;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  margin-left: 8px;
  min-width: 16px;
  text-align: center;
}

/* Trending section styling */
.trending-section {
  padding: 15px 0;
  border-bottom: 1px solid #f2f2f2;
}

/* Keyword suggestion tags - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */
.keyword-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
}

.keyword-suggestion-tag {
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.keyword-suggestion-tag:hover {
  background-color: #ffe8e3;
  color: #ee4d2d;
}

/* Keyword suggestions list - SETELAH klik "Lihat Lainnya" = bentuk list */
.keyword-suggestions-list {
  padding: 0;
}

.keyword-suggestions-list .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  border-bottom: none;
}

.keyword-suggestions-list .suggestion-item:hover {
  background-color: #f9f9f9;
}

.keyword-suggestions-list .suggestion-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 14px;
  color: #999;
}

.keyword-suggestions-list .suggestion-text {
  font-size: 14px;
  color: #333;
}

/* Trending items styling */
.trending-item .suggestion-icon i {
  color: #ee4d2d !important;
}

/* Keyword suggestions popup styling - sesuai docs/facet.html */
.keyword-suggestions-popup {
  padding: 15px 0;
  border-top: 1px solid #f2f2f2;
  margin-top: 15px;
}

.keyword-suggestions-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 15px;
  font-weight: 500;
}

/* Trending title styling - sesuai docs/facet.html */
.trending-title {
  margin-top: 15px;
  color: #333;
  font-weight: 500;
  padding: 12px 15px 5px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.trending-title .trend-icon {
  margin-left: 5px;
  font-size: 13px;
  color: #ee4d2d;
}

/* Additional suggestions styling */
.additional-suggestions {
  max-height: none;
  overflow: visible;
  transition: max-height 0.3s ease;
  border-bottom: 0px solid #f2f2f2;
  background-color: white;
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
  display: block;
}

.additional-suggestions.open {
  max-height: none;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 20px;
}

.additional-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.additional-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

.additional-suggestions .suggestion-icon {
  font-size: 18px !important;
  margin-bottom: 6px;
  color: #ee4d2d; /* Warna oranye untuk ikon trend */
}

/* See more button styling - persis seperti docs/facet.html */
.see-more-container {
  padding: 10px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
  border-bottom: 1px solid #f2f2f2;
}

.see-more-btn {
  background-color: transparent;
  color: #ee4d2d;
  border: none;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  gap: 5px;
}

.see-more-btn:hover {
  background-color: #f9f9f9;
}

.see-more-btn i {
  color: #ee4d2d;
  font-size: 14px;
}

/* Product suggestions styling - sesuai docs/facet.html */
.product-suggestions {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}

.product-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 5px;
  display: block;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
  box-sizing: border-box;
  border: 2px solid #eee;
  border-radius: 5px;
  margin-top: 30px;
  padding: 10px 10px 25px 10px;
  transform: translateY(0px);
}

.simple-product-card {
  background-color: white;
  border-radius: 3px;
  transform: translateY(10px);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-align: center;
}

.simple-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.product-img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.simple-product-name {
  font-size: 13px;
  color: #333;
  padding: 10px 5px;
  text-align: center;
  height: auto;
  max-height: 40px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* Responsive */
@media (max-width: 480px) {
  .cart-icon, .chat-icon {
    font-size: 20px;
  }

  .cart-icon {
    -webkit-text-stroke: 0.8px white;
  }

  .chat-icon {
    -webkit-text-stroke: 1.2px white;
    margin-left: 15px;
  }

  .cart-badge, .chat-badge {
    width: 16px;
    height: 16px;
    font-size: 10px;
  }

  .chat-icon .chat-dots .dot {
    width: 2px;
    height: 2px;
  }
}
