"use client"

import { useState, useRef, useEffect } from "react"
import { Search, X, ArrowLeft, Clock, TrendingUp, ChevronDown, Plus, Minus, Filter, ShoppingCart, MessageCircle } from "lucide-react"
import { VelozioFilterTabs } from "./velozio-filter-tabs"
import { VelozioFacet } from "./velozio-facet"
import "./velozio-styles.css"

interface ActiveFilters {
  categories?: string[]
  priceRanges?: string[]
  ratings?: string[]
  shipping?: string[]
  features?: string[]
}

export function VelozioHeader() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchText, setSearchText] = useState("")
  const [focused, setFocused] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isListMode, setIsListMode] = useState(false)
  const [showFilterTabs, setShowFilterTabs] = useState(false)
  const [isSearchActive, setIsSearchActive] = useState(false)
  const [showKeywordPredictions, setShowKeywordPredictions] = useState(false)
  const [showNotFound, setShowNotFound] = useState(false)
  const [showFacet, setShowFacet] = useState(false)
  const [activeFilters, setActiveFilters] = useState<ActiveFilters>({})
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [showFilterIcon, setShowFilterIcon] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const expandedSearchInputRef = useRef<HTMLInputElement>(null)
  
  // Simulasi loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 800) // Menunggu 800ms untuk simulasi loading

    return () => clearTimeout(timer)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Restore body scroll on unmount
      document.body.classList.remove('suggestions-open')
    }
  }, [])

  // Placeholder texts for animation
  const placeholderTexts = [
    "Handphone Samsung",
    "Sepatu Pria",
    "Tas Wanita",
    "Promo Elektronik",
    "Laptop Gaming",
    "Kamera Mirrorless",
    "Smart TV Android",
    "Headphone Bluetooth",
  ]

  // Data untuk suggestions
  const searchHistory = [
    "Smartphone Android",
    "Sepatu Sneakers",
    "Tas Selempang",
    "Headphone Bluetooth",
    "Keyboard Gaming",
  ]

  const trendingSuggestions = [
    "iPhone 15 Pro Max",
    "Samsung Galaxy S24",
    "MacBook Air M3",
    "AirPods Pro 2",
    "iPad Air 5",
    "Apple Watch Series 9",
    "Sony WH-1000XM5",
    "Nintendo Switch OLED",
  ]

  const additionalSuggestions = [
    "Laptop ASUS ROG",
    "Kamera Canon EOS",
    "Drone DJI Mini",
    "Smartwatch Garmin",
    "Speaker JBL",
    "Mouse Logitech",
    "Monitor Gaming",
    "SSD External",
  ]

  // Sample products untuk suggestions - sesuai docs/facet.html
  const sampleProducts = [
    {
      id: 1,
      name: "Smartphone Android Samsung",
      price: "Rp 2.999.000",
      originalPrice: "Rp 3.499.000",
      discount: "15%",
      image: "/api/placeholder/200/200",
      category: "handphone",
      shortName: "Samsung Galaxy",
      isMall: true,
      rating: 4.9,
      sold: 125,
      shipping: "Pengiriman Instan",
      cod: true
    },
    {
      id: 2,
      name: "Sepatu Sneakers Pria",
      price: "Rp 299.000",
      originalPrice: "Rp 399.000",
      discount: "25%",
      image: "/api/placeholder/200/200",
      category: "sepatu pria",
      shortName: "Sneakers Pria",
      isMall: false,
      rating: 4.8,
      sold: 215,
      shipping: "Pengiriman Reguler",
      cod: true
    },
    {
      id: 3,
      name: "Tas Selempang Wanita",
      price: "Rp 179.000",
      originalPrice: "Rp 229.000",
      discount: "22%",
      image: "/api/placeholder/200/200",
      category: "tas wanita",
      shortName: "Tas Selempang",
      isMall: true,
      rating: 4.7,
      sold: 98,
      shipping: "Pengiriman Instan",
      cod: false
    },
    {
      id: 4,
      name: "Headphone Bluetooth Wireless",
      price: "Rp 549.000",
      originalPrice: "Rp 699.000",
      discount: "21%",
      image: "/api/placeholder/200/200",
      category: "elektronik",
      shortName: "Headphone Bluetooth",
      isMall: true,
      rating: 4.6,
      sold: 85,
      shipping: "Pengiriman Next Day",
      cod: true
    },
    {
      id: 5,
      name: "Keyboard Gaming Mechanical",
      price: "Rp 459.000",
      originalPrice: "Rp 559.000",
      discount: "18%",
      image: "/api/placeholder/200/200",
      category: "elektronik",
      shortName: "Keyboard Gaming",
      isMall: false,
      rating: 4.9,
      sold: 64,
      shipping: "Pengiriman Reguler",
      cod: false
    },
    {
      id: 6,
      name: "Power Bank Quick Charge",
      price: "Rp 229.000",
      originalPrice: "Rp 299.000",
      discount: "23%",
      image: "/api/placeholder/200/200",
      category: "handphone",
      shortName: "Power Bank",
      isMall: true,
      rating: 4.7,
      sold: 178,
      shipping: "Pengiriman Instan",
      cod: true
    },
    {
      id: 7,
      name: "Smart TV Android UHD",
      price: "Rp 7.499.000",
      originalPrice: "Rp 8.999.000",
      discount: "17%",
      image: "/api/placeholder/200/200",
      category: "elektronik",
      shortName: "Smart TV",
      isMall: true,
      rating: 4.8,
      sold: 25,
      shipping: "Pengiriman Next Day",
      cod: false
    },
    {
      id: 8,
      name: "Kacamata Polarized Anti UV",
      price: "Rp 250.000",
      originalPrice: "Rp 350.000",
      discount: "28%",
      image: "/api/placeholder/200/200",
      category: "aksesoris",
      shortName: "Kacamata Polarized",
      isMall: true,
      rating: 4.7,
      sold: 43,
      shipping: "Pengiriman Reguler",
      cod: true
    },
    {
      id: 9,
      name: "Robot Vacuum Cleaner Smart",
      price: "Rp 2.499.000",
      originalPrice: "Rp 2.999.000",
      discount: "17%",
      image: "/api/placeholder/200/200",
      category: "elektronik",
      shortName: "Robot Vacuum",
      isMall: false,
      rating: 4.5,
      sold: 42,
      shipping: "Pengiriman Reguler",
      cod: true
    }
  ]

  // Keyword predictions database - sesuai docs/facet.html
  const keywordPredictionDB = {
    // Database sinonim
    synonyms: {
      "smartphone": ["handphone", "hp", "ponsel"],
      "sepatu": ["shoes", "footwear"],
      "tas": ["bag", "handbag"],
      "headphone": ["earphone", "audio"],
      "keyboard": ["papan ketik"],
      "tv": ["televisi", "television"],
      "kacamata": ["glasses", "eyewear"]
    },

    // Database kata terkait
    relatedKeywords: {
      "smartphone": ["android", "samsung", "iphone", "xiaomi"],
      "sepatu": ["sneakers", "pria", "wanita", "sport"],
      "tas": ["selempang", "ransel", "wanita", "kulit"],
      "headphone": ["bluetooth", "wireless", "gaming"],
      "keyboard": ["gaming", "mechanical", "wireless"],
      "tv": ["smart", "android", "uhd", "4k"],
      "kacamata": ["polarized", "anti", "uv", "sunglasses"]
    },

    // Database koreksi typo
    typoCorrections: {
      "smartfone": "smartphone",
      "handpone": "handphone",
      "septu": "sepatu",
      "hedphone": "headphone",
      "keybord": "keyboard",
      "kacmata": "kacamata"
    }
  }

  // Fungsi untuk generate keyword predictions berdasarkan input
  const generateKeywordPredictions = (input: string) => {
    const inputLower = input.toLowerCase().trim()
    const results: Array<{text: string, type: string, relevance: number}> = []

    if (inputLower.length === 0) return []

    // 1. Cari dari history yang cocok
    searchHistory.forEach(item => {
      if (item.toLowerCase().includes(inputLower)) {
        results.push({
          text: item,
          type: "history",
          relevance: 100
        })
      }
    })

    // 2. Cari dari nama produk
    sampleProducts.forEach(product => {
      const productName = product.name.toLowerCase()
      const category = product.category.toLowerCase()

      if (productName.includes(inputLower) || category.includes(inputLower)) {
        // Tambahkan nama produk
        if (productName.includes(inputLower)) {
          results.push({
            text: product.name,
            type: "product",
            relevance: 90
          })
        }

        // Tambahkan kategori
        if (category.includes(inputLower)) {
          results.push({
            text: product.category,
            type: "product",
            relevance: 85
          })
        }
      }
    })

    // 3. Cari dari trending suggestions
    trendingSuggestions.forEach(item => {
      if (item.toLowerCase().includes(inputLower)) {
        results.push({
          text: item,
          type: "trending",
          relevance: 80
        })
      }
    })

    // 4. Cari dari sinonim
    Object.entries(keywordPredictionDB.synonyms).forEach(([key, synonyms]) => {
      if (key.includes(inputLower) || synonyms.some(syn => syn.includes(inputLower))) {
        results.push({
          text: key,
          type: "search",
          relevance: 75
        })

        synonyms.forEach(syn => {
          if (syn.includes(inputLower)) {
            results.push({
              text: syn,
              type: "search",
              relevance: 70
            })
          }
        })
      }
    })

    // 5. Cari dari kata terkait
    Object.entries(keywordPredictionDB.relatedKeywords).forEach(([key, related]) => {
      if (key.includes(inputLower)) {
        related.forEach(rel => {
          results.push({
            text: `${key} ${rel}`,
            type: "search",
            relevance: 65
          })
        })
      }
    })

    // 6. Koreksi typo
    Object.entries(keywordPredictionDB.typoCorrections).forEach(([typo, correction]) => {
      if (typo.includes(inputLower)) {
        results.push({
          text: correction,
          type: "search",
          relevance: 60
        })
      }
    })

    // Remove duplicates dan sort by relevance
    const uniqueResults = results.filter((item, index, self) =>
      index === self.findIndex(t => t.text.toLowerCase() === item.text.toLowerCase())
    )

    return uniqueResults
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 12) // Batasi maksimal 12 hasil sesuai facet.html
  }

  const handleSearchClick = () => {
    setIsExpanded(true)
    setShowSuggestions(true)
    // Show overlay and prevent body scroll
    const overlay = document.querySelector('.overlay')
    if (overlay) overlay.classList.add('show')
    document.body.classList.add('show-suggestions')
    // Focus on the expanded search input after a short delay
    setTimeout(() => {
      if (expandedSearchInputRef.current) {
        expandedSearchInputRef.current.focus()
      }
    }, 100)
  }

  const handleBackClick = () => {
    setIsExpanded(false)
    setSearchText("")
    setShowSuggestions(false)
    setShowFilterTabs(false)
    setIsSearchActive(false)
    setShowKeywordPredictions(false)
    setShowNotFound(false)
    setShowFilterIcon(false) // PERBAIKAN: Reset ke icon search saat back
    setShowFacet(false) // Close facet panel jika terbuka
    setIsListMode(false) // Reset ke button mode
    // Hide overlay and restore body scroll
    const overlay = document.querySelector('.overlay')
    if (overlay) overlay.classList.remove('show')
    document.body.classList.remove('show-suggestions')
  }

  const handleClearClick = () => {
    setSearchText("")
    setShowSuggestions(false)
    setShowKeywordPredictions(false)
    setShowFilterTabs(false)
    setIsSearchActive(false)
    setShowNotFound(false)
    setShowFilterIcon(false) // PERBAIKAN: Reset ke icon search saat clear
    setIsListMode(false) // Reset ke button mode
    // Hide overlay and restore body scroll
    const overlay = document.querySelector('.overlay')
    if (overlay) overlay.classList.remove('show')
    document.body.classList.remove('show-suggestions')
    if (expandedSearchInputRef.current) {
      expandedSearchInputRef.current.focus()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchText(value)

    // PERBAIKAN: Reset ke icon search saat user mengetik atau mengubah input
    setShowFilterIcon(false)
    setIsListMode(false) // Reset ke button mode

    // Perilaku berdasarkan panjang input sesuai facet.html
    if (value.length === 0) {
      // Input kosong - tampilkan suggestions normal, sembunyikan predictions
      setShowSuggestions(true)
      setShowKeywordPredictions(false)
      setShowFilterTabs(false)
      setIsSearchActive(false)
      setShowNotFound(false)
      setShowFilterIcon(false) // Reset ke icon search
      setIsListMode(false) // Reset ke button mode
      // Show overlay and prevent body scroll
      const overlay = document.querySelector('.suggestions-overlay')
      if (overlay) overlay.classList.add('show')
      document.body.classList.add('show-suggestions')
    } else if (value.length >= 1) {
      // Mulai mengetik - tampilkan keyword predictions, sembunyikan suggestions
      setShowSuggestions(false)
      setShowKeywordPredictions(true)
      setShowNotFound(false)
      setShowFilterIcon(false) // Reset ke icon search
      setIsListMode(false) // Reset ke button mode
      // Show overlay and prevent body scroll
      const overlay = document.querySelector('.suggestions-overlay')
      if (overlay) overlay.classList.add('show')
      document.body.classList.add('show-suggestions')
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setSearchText(suggestion)
    setShowSuggestions(false)
    setIsSearchActive(true)
    setShowFilterTabs(true)
    // Restore body scroll
    document.body.classList.remove('suggestions-open')
    // Here you would typically trigger a search
    console.log("Searching for:", suggestion)
  }

  const performSearch = (query: string) => {
    // Simulate search results based on query
    const searchLower = query.toLowerCase()
    const results = sampleProducts.filter(product =>
      product.name.toLowerCase().includes(searchLower) ||
      product.category.toLowerCase().includes(searchLower) ||
      product.shortName.toLowerCase().includes(searchLower)
    )

    setSearchResults(results)
    return results
  }

  const handleSearch = () => {
    if (searchText.trim()) {
      setIsSearchActive(true)
      setShowFilterTabs(true)
      setShowSuggestions(false)
      setShowKeywordPredictions(false)
      setShowFilterIcon(true)

      // Hide overlay
      const overlay = document.querySelector('.suggestions-overlay')
      if (overlay) overlay.classList.remove('show')

      // Perform search and get results
      const results = performSearch(searchText)

      // Check if results found
      if (results.length === 0) {
        setShowNotFound(true)
        setShowFilterTabs(false)
        setShowFilterIcon(false)
      } else {
        setShowNotFound(false)
      }

      console.log("Executing search for:", searchText, "Results:", results.length)
    }
  }

  const handleFiltersChange = (filters: ActiveFilters) => {
    setActiveFilters(filters)
    // Apply filters to search results
    if (searchResults.length > 0) {
      const filtered = applyFilters(searchResults, filters)
      console.log("Filtered results:", filtered.length)
    }
  }

  const applyFilters = (results: any[], filters: ActiveFilters) => {
    return results.filter(product => {
      // Category filter
      if (filters.categories && filters.categories.length > 0) {
        if (!filters.categories.includes(product.category)) return false
      }

      // Price range filter
      if (filters.priceRanges && filters.priceRanges.length > 0) {
        const price = parseFloat(product.price?.replace(/[^\d]/g, '') || '0')
        let matchesPrice = false

        filters.priceRanges.forEach(range => {
          if (range === "Di bawah Rp 100.000" && price < 100000) matchesPrice = true
          if (range === "Rp 100.000 - Rp 500.000" && price >= 100000 && price < 500000) matchesPrice = true
          if (range === "Rp 500.000 - Rp 1.000.000" && price >= 500000 && price < 1000000) matchesPrice = true
          if (range === "Rp 1.000.000 - Rp 5.000.000" && price >= 1000000 && price < 5000000) matchesPrice = true
          if (range === "Di atas Rp 5.000.000" && price >= 5000000) matchesPrice = true
        })

        if (!matchesPrice) return false
      }

      // Rating filter
      if (filters.ratings && filters.ratings.length > 0) {
        const rating = product.rating || 0
        let matchesRating = false

        filters.ratings.forEach(ratingFilter => {
          if (ratingFilter === "5 Bintang" && rating >= 5) matchesRating = true
          if (ratingFilter === "4 Bintang ke atas" && rating >= 4) matchesRating = true
          if (ratingFilter === "3 Bintang ke atas" && rating >= 3) matchesRating = true
        })

        if (!matchesRating) return false
      }

      // Features filter
      if (filters.features && filters.features.length > 0) {
        let hasRequiredFeatures = true

        filters.features.forEach(feature => {
          if (feature === "COD" && !product.cod) hasRequiredFeatures = false
          if (feature === "SellZio Mall" && !product.isMall) hasRequiredFeatures = false
        })

        if (!hasRequiredFeatures) return false
      }

      return true
    })
  }

  const handleFilterIconClick = () => {
    setShowFacet(true)
  }

  const handleCloseFacet = () => {
    setShowFacet(false)
  }

  const handleTryAnotherKeyword = () => {
    setSearchText("")
    setShowNotFound(false)
    setShowSuggestions(true)
    setShowFilterTabs(false)
    setIsSearchActive(false)
    if (expandedSearchInputRef.current) {
      expandedSearchInputRef.current.focus()
    }
  }

  const handleTrySuggestion = () => {
    setShowNotFound(false)
    setShowSuggestions(true)
    setShowFilterTabs(false)
    setIsSearchActive(false)
  }

  const handleSeeMoreClick = () => {
    setIsListMode(!isListMode)
    // Jangan ubah showMoreSuggestions, hanya toggle isListMode
  }

  const handleClearHistory = () => {
    // Here you would clear the search history
    console.log("Clearing search history")
  }

  const handleFocus = () => {
    setFocused(true)
    // PERBAIKAN: Reset ke icon search saat focus pada input
    setShowFilterIcon(false)
    setIsListMode(false) // Reset ke button mode
    if (searchText.length === 0) {
      setShowSuggestions(true)
      // Prevent body scroll when suggestions are shown
      document.body.classList.add('suggestions-open')
    }
  }

  const handleBlur = () => {
    setFocused(false)
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      setShowSuggestions(false)
      // Restore body scroll
      document.body.classList.remove('suggestions-open')
    }, 200)
  }

  // Filter suggestions based on search text
  const getFilteredSuggestions = () => {
    if (searchText.length === 0) {
      return searchHistory.slice(0, 5)
    }

    const filtered = [...searchHistory, ...trendingSuggestions, ...additionalSuggestions]
      .filter(item => item.toLowerCase().includes(searchText.toLowerCase()))
      .slice(0, 8)

    return filtered
  }

  // Get filtered keyword predictions - menggunakan fungsi baru
  const getFilteredPredictions = () => {
    if (searchText.length === 0) {
      return []
    }

    return generateKeywordPredictions(searchText)
  }

  // Fungsi untuk highlight text yang cocok - sesuai docs/facet.html
  const highlightMatchingText = (text: string, input: string) => {
    if (!input || input.trim() === '') return text

    let result = text

    // Kasus khusus untuk format "kata1 kata2" (spasi di tengah, bukan di akhir)
    if (input.includes(' ') && !input.endsWith(' ')) {
      const lastSpaceIndex = input.lastIndexOf(' ')
      const beforeSpace = input.substring(0, lastSpaceIndex).trim().toLowerCase()
      const afterSpace = input.substring(lastSpaceIndex + 1).trim().toLowerCase()

      if (beforeSpace && afterSpace) {
        // Cari apakah teks dimulai dengan kombinasi kata
        const searchPattern = (beforeSpace + ' ' + afterSpace).toLowerCase()
        const textLower = text.toLowerCase()

        if (textLower.startsWith(searchPattern)) {
          // Highlight seluruh pola pencarian
          const patternLength = searchPattern.length
          return `<span class="highlighted">${text.substring(0, patternLength)}</span>${text.substring(patternLength)}`
        }
        else if (textLower.startsWith(beforeSpace)) {
          // Highlight kata pertama + huruf awal dari kata kedua
          const startOfSecond = textLower.indexOf(beforeSpace + ' ') + beforeSpace.length + 1
          const endHighlight = startOfSecond + afterSpace.length

          if (startOfSecond > beforeSpace.length) {
            return `<span class="highlighted">${text.substring(0, endHighlight)}</span>${text.substring(endHighlight)}`
          }
        }
      }
    }

    // Persiapkan input untuk highlight
    const inputLower = input.toLowerCase().trim()
    const words = inputLower.split(' ').filter(word => word.length > 0)

    // Jika input diakhiri dengan spasi, highlight semua kata sebelumnya
    if (input.endsWith(' ')) {
      if (words.length > 0) {
        // Ambil semua kata input tanpa spasi di akhir
        const highlightPhrase = words.join(' ')

        // Periksa apakah teks dimulai dengan frasa input
        const textLower = text.toLowerCase()

        if (textLower.startsWith(highlightPhrase)) {
          // Highlight frasa input di awal teks
          const phraseLength = highlightPhrase.length
          result = `<span class="highlighted">${text.substring(0, phraseLength)}</span>${text.substring(phraseLength)}`
        } else {
          // Coba highlight frasa di mana saja dalam teks
          const startIndex = textLower.indexOf(highlightPhrase)
          if (startIndex >= 0) {
            const endIndex = startIndex + highlightPhrase.length
            result = `${text.substring(0, startIndex)}<span class="highlighted">${text.substring(startIndex, endIndex)}</span>${text.substring(endIndex)}`
          }
        }

        return result
      }
    }

    // Jika tidak diakhiri dengan spasi, gunakan logika asli
    if (!input.endsWith(' ')) {
      // Ambil kata terakhir yang sedang diketik
      const lastWord = words[words.length - 1]

      // Pecah teks menjadi kata-kata
      const textWords = text.toLowerCase().split(' ')

      // Buat array untuk menyimpan kata-kata dengan highlight
      const highlightedWords: string[] = []

      // Periksa setiap kata dalam teks
      textWords.forEach((textWord, index) => {
        // Jika kata ini dimulai dengan kata terakhir yang diketik pengguna
        if (textWord.startsWith(lastWord)) {
          // Highlight awal kata sesuai dengan apa yang diketik pengguna
          const highlighted = `<span class="highlighted">${text.split(' ')[index].substring(0, lastWord.length)}</span>${text.split(' ')[index].substring(lastWord.length)}`
          highlightedWords.push(highlighted)
        }
        // Jika kata sebelumnya dari input cocok dengan kata di teks
        else if (words.length > 1 && words.slice(0, -1).some(w => textWord === w)) {
          highlightedWords.push(`<span class="highlighted">${text.split(' ')[index]}</span>`)
        }
        else {
          highlightedWords.push(text.split(' ')[index])
        }
      })

      // Gabungkan kembali kata-kata
      result = highlightedWords.join(' ')
    }
    // Case 2: Input dengan spasi akhir (selesai mengetik kata)
    else {
      // Highlight semua kata yang cocok dengan kata input lengkap
      words.forEach(word => {
        if (word.length < 1) return

        // Match kata lengkap
        const textParts = text.split(' ')
        const textPartsLower = text.toLowerCase().split(' ')

        for (let i = 0; i < textPartsLower.length; i++) {
          if (textPartsLower[i] === word) {
            textParts[i] = `<span class="highlighted">${textParts[i]}</span>`
          }
        }

        result = textParts.join(' ')
      })
    }

    return result
  }

  // Fungsi untuk mengecek apakah prediction mengandung keyword utama - sesuai docs/facet.html
  const containsMainKeyword = (input: string, prediction: string) => {
    if (!input || input.trim() === '') return false

    const inputLower = input.toLowerCase().trim()
    const predictionLower = prediction.toLowerCase()

    // Untuk input dengan format "kata1 kata2" (spasi di tengah)
    if (input.includes(' ') && !input.endsWith(' ')) {
      const lastSpaceIndex = input.lastIndexOf(' ')
      const beforeSpace = input.substring(0, lastSpaceIndex).trim().toLowerCase()

      // Periksa apakah prediksi dimulai dengan kata pertama atau mengandung kata pertama
      return predictionLower.startsWith(beforeSpace) || predictionLower.includes(beforeSpace)
    }

    // Untuk input dengan spasi di akhir, periksa keseluruhan frasa
    if (input.endsWith(' ')) {
      const inputPhrase = input.trim().toLowerCase()
      return predictionLower.startsWith(inputPhrase) || predictionLower.includes(inputPhrase)
    }

    // Untuk input normal (single word atau partial word)
    const inputWords = inputLower.split(' ')

    // Kata pertama selalu dianggap sebagai kata kunci utama
    if (inputWords.length > 0) {
      const mainWord = inputWords[0]
      if (mainWord.length >= 1) {
        // Periksa apakah prediksi mengandung atau dimulai dengan kata kunci utama
        return predictionLower.includes(mainWord) || predictionLower.startsWith(mainWord)
      }
    }

    // Fallback jika tidak ada kata yang cukup panjang
    return false
  }

  // Get icon for prediction type - sesuai docs/facet.html
  const getPredictionIcon = (type: string) => {
    switch (type) {
      case 'history':
        return 'fa-history'
      case 'trending':
        return 'fa-arrow-trend-up'
      case 'product':
        return 'fa-cart-shopping' // Sesuai dengan docs/facet.html
      case 'search':
        return 'fa-search'
      default:
        return 'fa-search'
    }
  }

  return (
    <>
      <style jsx global>{`
        @keyframes placeholderAnimation {
          0% {
            opacity: 0;
            transform: translateY(20px);
            visibility: hidden;
          }
          1%, 5% {
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
          }
          6%, 100% {
            opacity: 0;
            transform: translateY(-20px);
            visibility: hidden;
          }
        }

        .search-placeholder {
          position: absolute;
          left: 15px;
          top: 50%;
          transform: translateY(-50%);
          color: #ee4d2d;
          pointer-events: none;
          font-size: 14px;
          transition: opacity 0.3s ease-in-out;
          display: flex !important;
          flex-direction: column;
          width: calc(100% - 60px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 20px;
          z-index: 11;
          opacity: 0; /* Start hidden */
          animation: fadeIn 0.5s ease-in-out 0.3s forwards; /* Fade in after page loads */
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        .placeholder-dynamic {
          position: relative;
          width: 100%;
          overflow: hidden;
          height: 20px;
          max-width: calc(100% - 60px);
          line-height: 1.2;
          opacity: 1;
          transition: opacity 0.3s ease-in-out;
        }

        .placeholder-text {
          position: absolute;
          width: 100%;
          display: flex;
          align-items: center;
          opacity: 0;
          visibility: hidden;
          top: 0;
          left: 0;
          animation: placeholderAnimation 45s infinite; /* 45 detik total untuk 15 item */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          will-change: transform, opacity; /* Optimize for animation performance */
        }

        /* Delay animation untuk setiap placeholder text */
        .placeholder-text:nth-child(1) { animation-delay: 0s; }
        .placeholder-text:nth-child(2) { animation-delay: 3s; }
        .placeholder-text:nth-child(3) { animation-delay: 6s; }
        .placeholder-text:nth-child(4) { animation-delay: 9s; }
        .placeholder-text:nth-child(5) { animation-delay: 12s; }
        .placeholder-text:nth-child(6) { animation-delay: 15s; }
        .placeholder-text:nth-child(7) { animation-delay: 18s; }
        .placeholder-text:nth-child(8) { animation-delay: 21s; }
        
        /* Skeleton loading animation */
        @keyframes shimmer {
          0% { background-position: -468px 0; }
          100% { background-position: 468px 0; }
        }
      `}</style>

      {/* Main header - shown when not expanded */}
      {!isExpanded && (
        <header className="fixed top-0 left-0 w-full bg-[#ee4d2d] py-3 px-4 z-50">
          <div className="w-full max-w-[800px] mx-auto relative">
            <div
              className="flex items-center relative rounded-lg border-2 border-[#ee4d2d] shadow-sm"
              onClick={handleSearchClick}
              style={{
                backgroundColor: "#FFFFFF",
                width: "calc(100% - 80px)" /* Mengurangi lebar untuk memberikan ruang bagi ikon */,
                position: "relative",
              }}
            >
              <input
                type="text"
                className="w-[calc(100%-30px)] py-2 px-4 pr-10 rounded-lg text-sm outline-none bg-white text-gray-800 search-input"
                placeholder=" "
                readOnly
                ref={searchInputRef}
                style={{
                  backgroundColor: "#FFFFFF",
                  color: "#333333",
                }}
                id="searchInput"
              />

              {/* Skeleton loading atau animated placeholder */}
              {isLoading ? (
                <div className="search-placeholder" style={{
                  background: 'linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%)',
                  backgroundSize: '800px 104px',
                  animation: 'shimmer 1.5s infinite linear',
                  width: '70%',
                  height: '14px',
                  borderRadius: '4px'
                }}></div>
              ) : (
                <div className="search-placeholder" id="searchPlaceholder">
                  <div className="placeholder-dynamic">
                    {placeholderTexts.map((text, index) => (
                      <div key={index} className="placeholder-text">
                        {text}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="absolute right-3 top-1/2 -translate-y-1/2 text-[#ee4d2d]">
                <Search size={18} strokeWidth={2} />
              </div>
            </div>

            {/* Cart and Chat icons - USING SVG DIRECTLY */}
            <div className="absolute top-1/2 -translate-y-1/2 flex items-center z-10 pt-[5px]" style={{ right: "8px" }}>
              {/* Cart icon with badge */}
              <div className="relative">
                {/* Custom SVG for shopping cart */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 576 512"
                  className="cursor-pointer"
                >
                  <path
                    fill="#ee4d2d"
                    stroke="white"
                    strokeWidth="30"
                    d="M0 24C0 10.7 10.7 0 24 0H69.5c22 0 41.5 12.8 50.6 32h411c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3H170.7l5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5H488c13.3 0 24 10.7 24 24s-10.7 24-24 24H199.7c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5H24C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z"
                  />
                </svg>

                {/* Badge in front of icon - moved slightly upward */}
                <div className="absolute -top-3 -right-2 bg-white text-[#ee4d2d] text-[11px] font-bold rounded-full w-[18px] h-[18px] flex items-center justify-center z-20">
                  5
                </div>
              </div>

              {/* Chat icon with badge - reduced margin */}
              <div className="relative ml-[15px]">
                {/* Custom SVG for comment with custom dots */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="22"
                  height="22"
                  viewBox="0 0 512 512"
                  className="cursor-pointer"
                >
                  <path
                    fill="#ee4d2d"
                    stroke="white"
                    strokeWidth="30"
                    d="M256 448c141.4 0 256-93.1 256-208S397.4 32 256 32S0 125.1 0 240c0 45.1 17.7 86.8 47.7 120.9c-1.9 24.5-11.4 46.3-21.4 62.9c-5.5 9.2-11.1 16.6-15.2 21.6c-2.1 2.5-3.7 4.4-4.9 5.7c-.6 .6-1 1.1-1.3 1.4l-.3 .3 0 0 0 0 0 0 0 0c-4.6 4.6-5.9 11.4-3.4 17.4c2.5 6 8.3 9.9 14.8 9.9c28.7 0 57.6-8.9 81.6-19.3c22.9-10 42.4-21.9 54.3-30.6c31.8 11.5 67 17.9 104.1 17.9z"
                  />
                </svg>

                {/* Custom dots (thinner) */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[60%] h-[30%] flex justify-between items-center z-10">
                  <div className="w-[2px] h-[2px] bg-white rounded-full"></div>
                  <div className="w-[2px] h-[2px] bg-white rounded-full"></div>
                  <div className="w-[2px] h-[2px] bg-white rounded-full"></div>
                </div>

                {/* Badge in front of icon - moved slightly upward */}
                <div className="absolute -top-3 -right-2 bg-white text-[#ee4d2d] text-[11px] font-bold rounded-full w-[18px] h-[18px] flex items-center justify-center z-20">
                  3
                </div>
              </div>
            </div>
          </div>
        </header>
      )}

      {/* Overlay Background */}
      <div className={`suggestions-overlay ${(showSuggestions || showKeywordPredictions || showNotFound) ? 'show' : ''}`}></div>

      {/* Expanded search header */}
      {isExpanded && (
        <>
          <div className="fixed top-0 left-0 w-full bg-white py-3 px-4 z-50 shadow-md">
            <div className="max-w-[800px] mx-auto flex items-center">
              <button className="text-[#ee4d2d] mr-3" onClick={handleBackClick}>
                <ArrowLeft size={20} className="transform scale-x-[1.3]" />
              </button>

              <div className="relative flex-1">
                <input
                  type="text"
                  className="w-full py-2 px-4 pr-10 rounded-lg text-sm border-2 border-[#ee4d2d] outline-none bg-white text-gray-800 search-input"
                  placeholder=" "
                  value={searchText}
                  onChange={handleInputChange}
                  ref={expandedSearchInputRef}
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  style={{ backgroundColor: "#FFFFFF", color: "#333333" }}
                />

                {/* Animated placeholder for expanded search */}
                {!focused && searchText === "" && !isLoading && (
                  <div className="search-placeholder">
                    <div className="placeholder-dynamic">
                      {placeholderTexts.map((text, index) => (
                        <div key={index} className="placeholder-text">
                          {text}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Skeleton loading untuk expanded search */}
                {!focused && searchText === "" && isLoading && (
                  <div className="search-placeholder" style={{
                    background: 'linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%)',
                    backgroundSize: '800px 104px',
                    animation: 'shimmer 1.5s infinite linear',
                    width: '70%',
                    height: '14px',
                    borderRadius: '4px'
                  }}></div>
                )}

                {/* Clear button - only show when there's text */}
                {searchText && (
                  <button
                    className="absolute right-12 top-1/2 -translate-y-1/2 bg-gray-400 rounded-full w-5 h-5 flex items-center justify-center"
                    onClick={handleClearClick}
                  >
                    <X size={12} className="text-white" />
                  </button>
                )}
              </div>

              {/* Search button or Filter button */}
              {showFilterIcon ? (
                <button
                  className="ml-3 bg-[#ee4d2d] text-white p-2 rounded-lg relative"
                  onClick={handleFilterIconClick}
                >
                  <Filter size={20} />
                  {Object.keys(activeFilters).length > 0 && (
                    <div className="absolute -top-1 -right-1 bg-white text-[#ee4d2d] text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                      {Object.values(activeFilters).reduce((total, values) => total + (values?.length || 0), 0)}
                    </div>
                  )}
                </button>
              ) : (
                <button className="ml-3 bg-[#ee4d2d] text-white p-2 rounded-lg" onClick={handleSearch}>
                  <Search size={20} />
                </button>
              )}
            </div>
          </div>

          {/* Suggestions Container */}
          {showSuggestions && (
            <div className="fixed top-[69px] left-0 right-0 bg-white shadow-lg z-40 w-full h-[calc(100vh-69px)]">
              <div className="suggestions-container max-w-[800px] mx-auto">
                {/* Search History / Text Suggestions */}
                {searchText.length === 0 ? (
                  <>
                    {/* Clear History Button */}
                    <div className="clear-history" onClick={handleClearHistory}>
                      Hapus riwayat pencarian
                    </div>

                    {/* Text Suggestions - Button Style (default) */}
                    <div className="text-suggestions" style={{ display: !isListMode ? 'block' : 'none' }}>
                      <div className="keyword-button-container">
                        {searchHistory.slice(0, 7).map((item, index) => (
                          <div
                            key={index}
                            className="keyword-button"
                            onClick={() => handleSuggestionClick(item)}
                          >
                            <div className="suggestion-icon">
                              <i className="fa fa-history"></i>
                            </div>
                            <span className="keyword-button-text">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Extended Suggestions - List Style (when expanded) */}
                    <div className="extended-suggestions" style={{ display: isListMode ? 'block' : 'none' }}>
                      {searchHistory.slice(0, 12).map((item, index) => (
                        <div
                          key={index}
                          className="suggestion-item"
                          onClick={() => handleSuggestionClick(item)}
                        >
                          <div className="suggestion-icon">
                            <i className="fa fa-history"></i>
                          </div>
                          <span className="suggestion-text">{item}</span>
                        </div>
                      ))}
                    </div>



                    {/* See More Button */}
                    <div className="see-more-container">
                      <button className="see-more-btn" onClick={handleSeeMoreClick}>
                        <div className="see-more-icon">
                          <i className={`fa ${isListMode ? 'fa-minus-circle' : 'fa-plus-circle'}`}></i>
                        </div>
                        <span>{isListMode ? 'Sembunyikan' : 'Lihat Lainnya'}</span>
                      </button>
                    </div>

                    {/* Trending Section */}
                    <div className="trending-title">
                      Sedang Trend
                      <TrendingUp className="trend-icon" size={16} />
                    </div>
                    <div className="additional-suggestions">
                      {trendingSuggestions.slice(0, 4).map((item, index) => (
                        <div
                          key={index}
                          className="suggestion-item"
                          onClick={() => handleSuggestionClick(item)}
                        >
                          <div className="suggestion-icon">
                            <i className="fa fa-arrow-trend-up"></i>
                          </div>
                          <span className="suggestion-text">{item}</span>
                        </div>
                      ))}
                    </div>
                  </>
                ) : (
                  /* Filtered Suggestions when typing */
                  <div className="text-suggestions">
                    {getFilteredSuggestions().map((item, index) => (
                      <div
                        key={index}
                        className="suggestion-item"
                        onClick={() => handleSuggestionClick(item)}
                      >
                        <div className="suggestion-icon">
                          <i className="fa fa-search"></i>
                        </div>
                        <span className="suggestion-text">{item}</span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Product Suggestions */}
                <div className="product-suggestions">
                  <div className="product-title">Produk Terkait</div>
                  <div className="product-grid">
                    {sampleProducts.map((product) => (
                      <div key={product.id} className="simple-product-card">
                        <img src={product.image} alt={product.name} className="product-img" />
                        <div className="simple-product-name">{product.name}</div>
                        <div className="product-price">{product.price}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Keyword Predictions Container */}
          {showKeywordPredictions && (
            <div className="fixed top-[69px] left-0 right-0 bg-white shadow-lg z-40 w-full h-[calc(100vh-69px)]">
              <div className="keyword-predictions-container max-w-[800px] mx-auto">
                {getFilteredPredictions().map((prediction, index) => {
                  // Generate highlighted text
                  const highlightedText = highlightMatchingText(prediction.text, searchText)

                  // Check if icon should be highlighted (orange)
                  const isRelevant = containsMainKeyword(searchText, prediction.text)

                  return (
                    <div
                      key={index}
                      className="prediction-item"
                      onClick={() => handleSuggestionClick(prediction.text)}
                    >
                      <div className={`prediction-icon ${isRelevant ? 'matched' : ''}`}>
                        <i className={`fa ${getPredictionIcon(prediction.type)}`}></i>
                      </div>
                      <span
                        className="prediction-text"
                        dangerouslySetInnerHTML={{ __html: highlightedText }}
                      />
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* Not Found Container */}
          {showNotFound && (
            <div className="fixed top-[69px] left-0 right-0 bg-white shadow-lg z-40 w-full h-[calc(100vh-69px)]">
              <div className="not-found-container max-w-[800px] mx-auto">
                <div className="not-found-icon">
                  <div className="search-document-icon">
                    <div className="document-base"></div>
                    <div className="document-fold"></div>
                    <div className="document-lines"></div>
                    <div className="magnifying-glass"></div>
                  </div>
                </div>
                <div className="not-found-title">Hasil tidak ditemukan</div>
                <div className="not-found-message">Mohon coba kata kunci yang lain atau yang lebih umum.</div>
                <div className="not-found-button-container">
                  <button className="not-found-button primary" onClick={handleTryAnotherKeyword}>
                    Coba kata kunci lain
                  </button>
                  <button className="not-found-button secondary" onClick={handleTrySuggestion}>
                    Coba produk lainnya
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Filter tabs - shown when search is active */}
          {showFilterTabs && !showNotFound && <VelozioFilterTabs className="fixed top-[69px] left-0 z-50" />}

          {/* Additional spacer for filter tabs */}
          <div className="h-[40px]"></div>
        </>
      )}

      {/* Facet Panel */}
      <VelozioFacet
        searchResults={searchResults}
        activeFilters={activeFilters}
        onFiltersChange={handleFiltersChange}
        isVisible={showFacet}
        onClose={handleCloseFacet}
      />

      {/* Spacer to push content below the fixed header */}
      <div className="h-[60px]"></div>
    </>
  )
}
