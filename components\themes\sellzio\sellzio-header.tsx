"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Search, ShoppingCart, MessageCircle, ArrowLeft, X } from 'lucide-react'

interface SellzioHeaderProps {
  onSearchFocus?: () => void
  onSearchBlur?: () => void
  onSearchChange?: (value: string) => void
  searchValue?: string
  isExpanded?: boolean
  onToggleExpanded?: () => void
}

export const SellzioHeader: React.FC<SellzioHeaderProps> = ({
  onSearchFocus,
  onSearchBlur,
  onSearchChange,
  searchValue = '',
  isExpanded = false,
  onToggleExpanded
}) => {
  const [inputValue, setInputValue] = useState(searchValue)
  const [showClearIcon, setShowClearIcon] = useState(false)
  const [placeholderIndex, setPlaceholderIndex] = useState(0)
  const [showPredictions, setShowPredictions] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Keyword predictions data - sesuai docs/facet.html
  const keywordPredictions = [
    'tas',
    'tas wanita',
    'tas pria',
    'tas sekolah',
    'tas ransel',
    'tas selempang',
    'tas laptop',
    'tas travel',
    'tas kulit',
    'tas branded',
    'tas murah',
    'tas import',
    'tas lokal',
    'tas vintage',
    'tas kantor'
  ]

  const searchHistory = ['tas', 'divf', 'tas mata', 't', 'tas sekolah', 'tas selempang']

  // Placeholder texts sesuai docs/facet.html
  const placeholderTexts = [
    "Cari produk, brand, atau toko",
    "Sepatu sneakers pria",
    "Tas wanita branded",
    "Smartphone terbaru",
    "Laptop gaming murah",
    "Headphone wireless",
    "Jam tangan pria",
    "Dress wanita cantik",
    "Kamera mirrorless",
    "Power bank 10000mah",
    "Earbuds bluetooth",
    "Smartwatch fitness",
    "Keyboard mechanical",
    "Mouse gaming",
    "Blender portable"
  ]

  // Animasi placeholder
  useEffect(() => {
    if (!inputValue && !isExpanded) {
      const interval = setInterval(() => {
        setPlaceholderIndex((prev) => (prev + 1) % placeholderTexts.length)
      }, 3000)
      return () => clearInterval(interval)
    }
  }, [inputValue, isExpanded, placeholderTexts.length])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    setShowClearIcon(value.length > 0)

    // Show predictions saat ketik minimal 1 huruf
    if (value.length > 0) {
      console.log('Setting showPredictions to true, value:', value)
      setShowPredictions(true)
    } else {
      console.log('Setting showPredictions to false')
      setShowPredictions(false)
    }

    onSearchChange?.(value)
  }

  const handleInputFocus = () => {
    onSearchFocus?.()
    onToggleExpanded?.()
  }

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Hanya trigger blur jika tidak dalam expanded mode
    if (!isExpanded) {
      onSearchBlur?.()
    }
  }

  const handleClearSearch = () => {
    setInputValue('')
    setShowClearIcon(false)
    setShowPredictions(false)
    onSearchChange?.('')
    inputRef.current?.focus()
  }

  // Function to get prediction icon
  const getPredictionIcon = (keyword: string, query: string) => {
    const isMatched = keyword.toLowerCase().includes(query.toLowerCase())

    if (keyword.includes('tas')) {
      return isMatched ? 'fa-cart-shopping matched' : 'fa-cart-shopping'
    } else if (searchHistory.includes(keyword)) {
      return isMatched ? 'fa-history matched' : 'fa-history'
    } else {
      return isMatched ? 'fa-arrow-trend-up matched' : 'fa-arrow-trend-up'
    }
  }

  // Function to highlight matching text
  const highlightMatchingText = (text: string, query: string) => {
    if (!query) return text

    const regex = new RegExp(`(${query})`, 'gi')
    const parts = text.split(regex)

    return parts.map((part, index) => {
      if (part.toLowerCase() === query.toLowerCase()) {
        return `<span class="highlighted">${part}</span>`
      }
      return part
    }).join('')
  }

  // Get filtered predictions
  const getFilteredPredictions = () => {
    if (!inputValue || inputValue.length === 0) {
      console.log('No input value, returning empty array')
      return []
    }

    const filtered = keywordPredictions.filter(keyword =>
      keyword.toLowerCase().includes(inputValue.toLowerCase())
    ).slice(0, 8) // Maksimal 8 prediksi

    console.log('Filtered predictions:', filtered, 'for input:', inputValue)
    return filtered
  }

  // Handle prediction click
  const handlePredictionClick = (keyword: string) => {
    setInputValue(keyword)
    setShowPredictions(false)
    onSearchChange?.(keyword)
  }

  const handleBackClick = () => {
    setInputValue('')
    setShowClearIcon(false)
    onSearchChange?.('')
    onToggleExpanded?.()
  }

  const handleSearchClick = () => {
    // Handle search action
    console.log('Search clicked:', inputValue)
  }

  if (isExpanded) {
    return (
      <div className="search-expanded">
        <div className="search-container">
          <button className="back-btn" onClick={handleBackClick}>
            <ArrowLeft size={20} />
          </button>
          <div className="search-input-wrapper">
            <input
              ref={inputRef}
              type="text"
              className="search-input"
              value={inputValue}
              onChange={handleInputChange}
              onBlur={handleInputBlur}
              placeholder="Cari produk, brand, atau toko"
              autoFocus
            />
            {showClearIcon && (
              <button className="clear-search-icon" onClick={handleClearSearch}>
                <X size={12} />
              </button>
            )}
          </div>
          <button className="expanded-search-icon" onClick={handleSearchClick}>
            <Search size={16} />
          </button>
        </div>
      </div>
    )
  }

  return (
    <header className="header">
      <div className="search-container">
        <div className="search-input-wrapper">
          <input
            ref={inputRef}
            type="text"
            className="search-input"
            value={inputValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            placeholder=""
          />
          
          {/* Animated placeholder */}
          {!inputValue && !isExpanded && (
            <div className="search-placeholder">
              <div className="placeholder-static">
                Cari di Sellzio
              </div>
              <div className="placeholder-dynamic">
                {placeholderTexts.map((text, index) => (
                  <div
                    key={index}
                    className={`placeholder-text ${index === placeholderIndex ? 'active' : ''}`}
                    style={{
                      animationDelay: `${index * 3}s`
                    }}
                  >
                    {text}
                  </div>
                ))}
              </div>
            </div>
          )}

          {showClearIcon && (
            <button className="clear-search-icon" onClick={handleClearSearch}>
              <X size={12} />
            </button>
          )}
          
          <button className="search-icon" onClick={handleSearchClick}>
            <Search size={16} />
          </button>
        </div>

      {/* Keyword Predictions Dropdown - muncul saat ketik minimal 1 huruf di expanded mode */}
      {(() => {
        console.log('Render check - showPredictions:', showPredictions, 'isExpanded:', isExpanded, 'filteredLength:', getFilteredPredictions().length)
        return null
      })()}
      {showPredictions && isExpanded && getFilteredPredictions().length > 0 && (
        <div className="keyword-predictions" id="keywordPredictions">
          {getFilteredPredictions().map((keyword, index) => {
            const iconClass = getPredictionIcon(keyword, inputValue)
            return (
              <div
                key={index}
                className="prediction-item"
                onClick={() => handlePredictionClick(keyword)}
              >
                <span className={`prediction-icon ${iconClass}`}>
                  <i className={`fa ${iconClass.split(' ')[0]}`}></i>
                </span>
                <span
                  className="prediction-text"
                  dangerouslySetInnerHTML={{
                    __html: highlightMatchingText(keyword, inputValue)
                  }}
                />
              </div>
            )
          })}
        </div>
      )}

        {/* Header Icons */}
        <div className="header-icons">
          <div className="cart-icon">
            <ShoppingCart size={22} />
            <span className="cart-badge">3</span>
          </div>
          <div className="chat-icon">
            <MessageCircle size={22} />
            <div className="chat-dots">
              <div className="dot"></div>
              <div className="dot"></div>
              <div className="dot"></div>
            </div>
            <span className="chat-badge">2</span>
          </div>
        </div>
      </div>
    </header>
  )
}
