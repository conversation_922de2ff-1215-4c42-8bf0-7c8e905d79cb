"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { Search, X, ShoppingCart, MessageCircle, ArrowLeft, Filter, Plus, Minus } from "lucide-react"
import { useTenantTheme } from "@/components/tenant/tenant-theme-provider"
import { cn } from "@/lib/utils"

interface SearchBarProps {
  onSearch?: (query: string) => void
  className?: string
}

export function VelozioSearchBar({ onSearch, className }: SearchBarProps) {
  const { theme } = useTenantTheme()
  const [searchValue, setSearchValue] = useState("")
  const [isExpanded, setIsExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const expandedSearchInputRef = useRef<HTMLInputElement>(null)
  
  // Tambahkan dan hapus class pada body saat expanded search aktif/non-aktif
  useEffect(() => {
    if (typeof document !== 'undefined') {
      if (isExpanded) {
        // Tambahkan style untuk menyembunyikan Categories dan VelozioPage
        const style = document.createElement('style');
        style.id = 'expanded-search-style';
        style.innerHTML = `
          [data-component-name="Categories"] { display: none !important; }
          main[data-component-name="VelozioPage"] { display: none !important; }
          body { overflow: hidden; }
        `;
        document.head.appendChild(style);
      } else {
        // Hapus style saat expanded search ditutup
        const style = document.getElementById('expanded-search-style');
        if (style) {
          style.remove();
        }
      }
    }
    
    return () => {
      if (typeof document !== 'undefined') {
        const style = document.getElementById('expanded-search-style');
        if (style) {
          style.remove();
        }
      }
    };
  }, [isExpanded]);

  // Fungsi untuk mencegah event klik di komponen terteruskan ke parent
  const preventDefaultAndPropagation = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // Placeholder suggestions
  const placeholderSuggestions = ["Handphone Samsung", "Sepatu Pria", "Tas Wanita", "Promo Elektronik"]

  // Sample search history
  const searchHistory = [
    "Smartphone Android",
    "Sepatu Sneakers",
    "Tas Selempang",
    "Headphone Bluetooth",
    "Keyboard Gaming",
  ]

  // Toggle expanded search view
  const toggleExpandedSearch = () => {
    setIsExpanded(true)
    setTimeout(() => {
      expandedSearchInputRef.current?.focus()
    }, 100)
  }

  // Close expanded search view
  const closeExpandedSearch = () => {
    setIsExpanded(false)
    setSearchValue("")
    setShowSuggestions(false)
  }

  // Clear search input
  const clearSearch = () => {
    setSearchValue("")
    expandedSearchInputRef.current?.focus()
  }

  // Handle search submission
  const handleSearch = () => {
    if (searchValue.trim()) {
      onSearch?.(searchValue)
      setShowSuggestions(false)
    }
  }

  // Handle suggestion click - mengarahkan ke produk sesuai facet.html
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)
    // Tidak menutup suggestion container saat click
    // Simulasi mengarahkan ke produk seperti facet.html
    onSearch?.(suggestion)
  }

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch()
    }
  }

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    setShowSuggestions(e.target.value.length > 0)
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        expandedSearchInputRef.current &&
        !expandedSearchInputRef.current.contains(event.target as Node) &&
        !(event.target as Element).closest(".suggestions-container")
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div className={cn("velozio-search-container", className)}>
      {/* Normal search bar */}
      {!isExpanded && (
        <div className="relative w-full max-w-3xl mx-auto">
          <div className="flex items-center relative bg-white rounded-lg shadow-sm">
            <input
              ref={searchInputRef}
              type="text"
              className="flex-grow py-2 px-4 pr-10 border-2 border-primary rounded-lg text-sm outline-none"
              placeholder=" "
              onClick={toggleExpandedSearch}
            />
            <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
              {/* Animated placeholder */}
              <div className="search-placeholder text-primary opacity-100 pointer-events-none">
                <div className="placeholder-dynamic">
                  {placeholderSuggestions.map((suggestion, index) => (
                    <div key={index} className="placeholder-text" style={{ animationDelay: `${index * 3}s` }}>
                      {suggestion}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="absolute inset-y-0 right-4 flex items-center">
              <Search className="h-4 w-4 text-primary" />
            </div>

            {/* Cart and chat icons */}
            <div className="absolute right-12 flex items-center space-x-4">
              <div className="relative">
                <ShoppingCart className="h-5 w-5 text-white stroke-[1.5px]" />
                <span className="absolute -top-2 -right-2 bg-white text-primary text-xs font-bold w-4 h-4 rounded-full flex items-center justify-center">
                  5
                </span>
              </div>
              <div className="relative">
                <MessageCircle className="h-5 w-5 text-white stroke-[1.5px]" />
                <span className="absolute -top-2 -right-2 bg-white text-primary text-xs font-bold w-4 h-4 rounded-full flex items-center justify-center">
                  3
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Expanded search view dengan solusi CSS yang lebih kuat untuk menutupi elemen lain */}
      {isExpanded && (
        <div 
          className="fixed inset-0 bg-white overflow-y-auto w-full h-full"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          style={{ 
            position: 'fixed', 
            top: 0, 
            left: 0, 
            right: 0,
            bottom: 0,
            width: '100vw', 
            height: '100vh',
            display: 'block',
            visibility: 'visible',
            opacity: 1,
            zIndex: 99999,
            backgroundColor: '#ffffff',
            isolation: 'isolate',
            transform: 'translateZ(0)',
            overscrollBehavior: 'contain'
          }}
        >
          <div className="p-4 border-b">
            <div className="flex items-center max-w-3xl mx-auto">
              <button onClick={closeExpandedSearch} className="mr-3 text-primary">
                <ArrowLeft className="h-5 w-5" />
              </button>

              <div className="relative flex-grow">
                <input
                  ref={expandedSearchInputRef}
                  type="text"
                  className="w-full py-2 px-4 pr-16 border-2 border-primary rounded-lg text-sm outline-none"
                  placeholder=" "
                  value={searchValue}
                  onChange={handleInputChange}
                  onKeyPress={handleKeyPress}
                />

                {/* Animated placeholder */}
                {!searchValue && (
                  <div className="absolute inset-y-0 left-4 flex items-center pointer-events-none">
                    <div className="search-placeholder text-primary opacity-100">
                      <div className="placeholder-dynamic">
                        {placeholderSuggestions.map((suggestion, index) => (
                          <div key={index} className="placeholder-text" style={{ animationDelay: `${index * 3}s` }}>
                            {suggestion}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Clear button */}
                {searchValue && (
                  <button
                    className="absolute inset-y-0 right-12 flex items-center justify-center w-6 h-6 rounded-full bg-gray-400 my-auto"
                    onClick={clearSearch}
                  >
                    <X className="h-3 w-3 text-white" />
                  </button>
                )}
              </div>

              {/* Search button */}
              <button className="ml-2 bg-primary text-white p-2 rounded-lg" onClick={handleSearch}>
                <Search className="h-5 w-5" />
              </button>

              {/* Filter button - only show when search has results */}
              {searchValue && (
                <button className="ml-2 bg-primary text-white p-2 rounded-lg">
                  <Filter className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>

          {/* Suggestions */}
          {showSuggestions && (
            <div 
              className="suggestions-container max-w-3xl mx-auto bg-white shadow-md rounded-b-lg mt-1 overflow-hidden"
              onClick={(e) => e.stopPropagation()} // Mencegah click container menutup suggestions
            >
              <div 
                className="text-center py-3 text-sm text-gray-500 border-b"
                onClick={() => console.log('Hapus riwayat pencarian')} // Fungsi untuk menghapus riwayat
              >
                Hapus riwayat pencarian
              </div>

              {/* Search history */}
              <div className="p-3 flex flex-wrap gap-2">
                {searchHistory.map((item, index) => (
                  <button
                    key={index}
                    className="flex items-center bg-white border border-gray-200 rounded-full px-3 py-1.5 text-sm"
                    onClick={() => handleSuggestionClick(item)}
                  >
                    <span className="text-gray-500 mr-2">
                      <Search className="h-3 w-3" />
                    </span>
                    {item}
                  </button>
                ))}
              </div>

              {/* See more button */}
              <div className="text-center py-3 border-t border-gray-100">
                <button 
                  className="text-primary flex items-center justify-center mx-auto"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Lihat lainnya clicked');
                  }}
                >
                  <span className="mr-1">+</span> Lihat Lainnya
                </button>
              </div>

              {/* Trending */}
              <div className="px-4 pt-2">
                <div className="inline-flex items-center bg-red-50 text-primary rounded-full px-4 py-1">
                  Sedang Trend
                  <span className="ml-2 bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                    5
                  </span>
                </div>
              </div>

              {/* Trending items */}
              <div className="p-3">
                {[
                  "Smartphone Android",
                  "Sepatu Sneakers",
                  "Tas Selempang",
                  "Headphone Bluetooth",
                  "Keyboard Gaming",
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center py-3 border-b border-gray-100 cursor-pointer"
                    onClick={() => handleSuggestionClick(item)}
                  >
                    <span className="text-primary mr-3">
                      <Search className="h-4 w-4" />
                    </span>
                    <span>{item}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
